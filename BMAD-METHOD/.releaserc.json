{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "./tools/semantic-release-sync-installer.js", ["@semantic-release/git", {"assets": ["package.json", "package-lock.json", "tools/installer/package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}