# Epics and Stories

## Epic 1: Foundational Context Tools

**Epic Goal:** To provide the AI with a comprehensive, read-only view of the entire budget, enabling it to offer informed, context-aware assistance.

  * **Story 1.1: Implement Get Accounts Tool**
    *As an AI assistant, I want to retrieve a list of all budget accounts and their balances, so that I understand the user's financial standing.*
  * **Story 1.2: Implement Get Categories Tool**
    *As an AI assistant, I want to retrieve all categories and category groups, so that I understand the user's budget structure.*
  * **Story 1.3: Implement Get Payees Tool**
    *As an AI assistant, I want to retrieve the list of all known payees, so that I can suggest them when creating new transactions.*
  * **Story 1.4: Implement Get Budget Month Details Tool**
    *As an AI assistant, I want to retrieve the detailed budget summary for a given month, so that I can answer questions about spending and budget adherence.*

## Epic 2: Core Entity Creation

**Epic Goal:** To enable the creation of all fundamental YNAB entities through the conversational interface.

  * **Story 2.1: Implement Create Payee Tool**
    *As an AI assistant, I want to create a new payee, so that the user can easily assign transactions to new merchants or individuals.*
  * **Story 2.2: Implement Create Category Tool**
    *As an AI assistant, I want to create a new budget category, so that the user can organize their spending in new ways.*
  * **Story 2.3: Implement Create Category Group Tool**
    *As an AI assistant, I want to create a new category group, so that the user can create major new sections in their budget.*
  * **Story 2.4: Implement Create Scheduled Transaction Tool**
    *As an AI assistant, I want to create a recurring scheduled transaction, so that the user can automate future income or expenses.*

## Epic 3: Advanced Data Modification

**Epic Goal:** To provide robust tools for modifying existing data, including support for complex scenarios like split transactions.

  * **Story 3.1: Implement Single Transaction Update Tool**
    *As an AI assistant, I want to update a single transaction's details (amount, payee, category, memo), so that I can correct or modify user entries.*
  * **Story 3.2: Implement Bulk Transaction Update Tool**
    *As an AI assistant, I want to update multiple transactions at once, so that I can efficiently perform bulk edits.*
  * **Story 3.3: Implement Category Budget Update Tool**
    *As an AI assistant, I want to update the budgeted amount for a category, so that I can help users adjust their budget plans.*
  * **Story 3.4: Enhance Transaction Tools for Splitting**
    *As an AI assistant, I want to create or update a transaction to be split across multiple categories, so that the user can accurately record complex purchases.*


