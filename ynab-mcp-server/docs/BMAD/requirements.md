# Requirements

## Functional

  * **FR1:** A suite of tools shall exist to provide the AI with a complete contextual understanding of the user's budget.
  * **FR2:** A suite of tools shall exist for creating core YNAB entities like payees, categories, and scheduled transactions.
  * **FR3:** A suite of tools shall exist for modifying existing transactions and category budgets.
  * **FR4:** The transaction creation and modification tools shall support splitting a single transaction across multiple categories.

## Non Functional

  * **NFR1:** All new tools must follow the existing architectural pattern (`MCPTool` class, Zod schema) found in the `src/tools/` directory.
  * **NFR2:** All interactions with the YNAB API must be performed via the official `ynab` SDK.

## Compatibility Requirements

  * **CR1:** The function signatures and input schemas of existing tools must not be changed in a way that breaks backward compatibility. Enhancements (like for splitting transactions) should be additive.
  * **CR2:** The addition of new tools must not negatively impact the performance or stability of the existing server.
