# Source Tree Integration

The new files will integrate seamlessly into the existing project structure.

```plaintext
ynab-mcp-server/
└── src/
    └── tools/
        ├── ApproveTransaction.ts         # Existing
        ├── CreateCategory.ts             # New
        ├── CreateCategoryGroup.ts        # New
        ├── CreatePayee.ts                # New
        ├── CreateScheduledTransaction.ts # New
        ├── CreateTransaction.ts          # Modified for splitting
        ├── GetAccounts.ts                # New
        ├── GetBudgetMonths.ts            # New
        ├── GetCategories.ts              # New
        ├── GetPayees.ts                  # New
        ├── GetUnapprovedTransactions.ts   # Existing
        ├── ListBudgets.ts                # Existing
        ├── UpdateCategoryBudget.ts       # New
        ├── UpdateTransaction.ts          # New
        ├── UpdateTransactions.ts         # New
        └── index.ts                      # Modified to export all new tools
```
