# Component Architecture

The following new tool components will be created.

## New Read Tools

  * `GetAccounts`
  * `GetCategories`
  * `GetPayees`
  * `GetBudgetMonths`

## New Create Tools

  * `CreatePayee`
  * `CreateCategory`
  * `CreateCategoryGroup`
  * `CreateScheduledTransaction`

## New Modify Tools

  * `UpdateTransaction`
  * `UpdateTransactions`
  * `UpdateCategoryBudget`

## Modified Tools

  * `CreateTransaction`: Will be enhanced to accept an array of `subtransactions` for creating split transactions.
