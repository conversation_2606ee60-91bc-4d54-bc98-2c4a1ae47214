# Detailed Stories

This document contains the detailed, development-ready user stories for the `ynab-mcp-server` enhancement project. Each story is expanded from the high-level descriptions in `epics-and-stories.md` and serves as the single source of truth for implementation.

---

## Story Template

To add a new story, copy and paste the template below and fill in the details.

```markdown
# Story {{EpicNum}}.{{StoryNum}}: {{Short Title Copied from Epic File specific story}}

## Status: {{ Draft | Approved | InProgress | Review | Done }}

## Story

- As a {{role}}
- I want {{action}}
- so that {{benefit}}

## Acceptance Criteria (ACs)

{{ Copy of Acceptance Criteria numbered list }}

## Tasks / Subtasks

- [ ] Task 1 (AC: # if applicable)
  - [ ] Subtask1.1...
- [ ] Task 2 (AC: # if applicable)
  - [ ] Subtask 2.1...
- [ ] Task 3 (AC: # if applicable)
  - [ ] Subtask 3.1...

## Dev Notes

[[LLM: populates relevant information, only what was pulled from actual artifacts from docs folder, relevant to this story. Do not invent information. Critical: If known add Relevant Source Tree info that relates to this story. If there were important notes from previous story that are relevant to this one, also include them here if it will help the dev agent. You do NOT need to repeat anything from coding standards or test standards as the dev agent is already aware of those. The dev agent should NEVER need to read the PRD or architecture documents or child documents though to complete this self contained story, because your critical mission is to share the specific items needed here extremely concisely for the Dev Agent LLM to comprehend with the least about of context overhead token usage needed.]]

### Testing

[[LLM: Scrum Master use `test-strategy-and-standards.md` to leave instruction for developer agent in the following concise format, leave unchecked if no specific test requirement of that type]]
Dev Note: Story Requires the following tests:

- [ ] {{type f.e. Jest}} Unit Tests: (nextToFile: {{true|false}}), coverage requirement: {{from strategy or default 80%}}
- [ ] {{type f.e. Jest with in memory db}} Integration Test (Test Location): location: {{Integration test location f.e. `/tests/story-name/foo.spec.cs` or `next to handler`}}
- [ ] {{type f.e. Cypress}} E2E: location: {{f.e. `/e2e/{epic-name/bar.test.ts`}}

Manual Test Steps: [[LLM: Include how if possible the user can manually test the functionality when story is Ready for Review, if any]]

{{ f.e. `- dev will create a script with task 3 above that you can run with "npm run test-initiate-launch-sequence" and validate Armageddon is initiated`}}

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update]]
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update - remove this line to the SM]]
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log

[[LLM: (SM Agent) When Drafting Story, leave next prompt in place for dev agent to remove and update- remove this line to the SM]]
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
```

---
---

# Story 1.1: Implement Get Accounts Tool

## Status: Approved

## Story

- As an AI assistant
- I want to retrieve a list of all budget accounts and their balances
- so that I can understand the user's financial standing.

## Acceptance Criteria (ACs)

1.  A new tool named `GetAccountsTool` is created and extends `MCPTool`.
2.  The tool successfully retrieves all open (not closed or deleted) accounts for a given budget ID using the YNAB API.
3.  The tool accepts an optional `budgetId` as input. If not provided, it defaults to the `YNAB_BUDGET_ID` environment variable.
4.  If no budget ID is available (neither as input nor in the environment), the tool returns a clear error message instructing the user to provide one.
5.  The tool returns a structured list of accounts, including at least the following fields for each account: `id`, `name`, `type`, `balance`, `cleared_balance`, and `uncleared_balance`.
6.  The tool is integrated into `src/index.ts` so it is available to the `MCPServer`.

## Tasks / Subtasks

- [ ] Create file `src/tools/GetAccountsTool.ts` with `GetAccountsTool` class extending `MCPTool`. (AC: 1)
- [ ] Implement the tool's Zod schema for the optional `budgetId` input. (AC: 3)
- [ ] Implement the `execute` method to fetch accounts from the YNAB API. (AC: 2)
- [ ] Add logic to filter out closed and deleted accounts from the API response. (AC: 2)
- [ ] Add logic to handle the `budgetId` (prioritizing input over the environment variable). (AC: 3)
- [ ] Add error handling for a missing `budgetId`. (AC: 4)
- [ ] Format the output to return a clean list of account objects. (AC: 5)
- [ ] Import and register the new tool in `src/index.ts`. (AC: 6)

## Dev Notes

This tool is foundational for providing the AI with context. It's a read-only operation and should follow the additive-only pattern described in `enhancement-scope-and-integration-strategy.md`. The implementation should be placed in a new file, `src/tools/GetAccountsTool.ts`, and use the existing `ynab` SDK.

### Testing

Dev Note: Story Requires the following tests:

- [X] Vitest Unit Tests: (nextToFile: true), coverage requirement: 80%
- [ ] Integration Test: Not required. API interactions should be mocked.
- [ ] E2E Test: Not required.

Manual Test Steps:

- After implementation, run the server via `npm start`.
- Use a tool like `curl` or Postman to send a POST request to the server for the `get_accounts` tool.
- Example with `curl`: `curl -X POST -H "Content-Type: application/json" -d '{"tool": "get_accounts", "input": {}}' http://localhost:3000/`
- Verify the response contains a JSON object with an `accounts` array.
- Test again, providing a specific `budgetId`: `curl -X POST -H "Content-Type: application/json" -d '{"tool": "get_accounts", "input": {"budgetId": "your_budget_id"}}' http://localhost:3000/`

## Dev Agent Record

### Agent Model Used: 
[[LLM: (Dev Agent) Update with model name/version on completion]]

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |
``` 