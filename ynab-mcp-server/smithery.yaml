# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - ynabApiToken
    properties:
      ynabApiToken:
        type: string
        description: Your YNAB Personal Access Token
      ynabBudgetId:
        type: string
        description: Optional budget id for the specific budget you want to interact with
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => ({
      command: 'node',
      args: ['dist/index.js'],
      env: {
        YNAB_API_TOKEN: config.ynabApiToken,
        YNAB_BUDGET_ID: config.ynabBudgetId || ''
      }
    })
  exampleConfig:
    ynabApiToken: your-ynab-personal-access-token
    ynabBudgetId: your-ynab-budget-id
