import { MCPTool, logger } from "mcp-framework";
import { z } from "zod";
import * as ynab from "ynab";
import { Account } from "ynab";

interface GetAccountsToolInput {
  budgetId?: string;
}

class GetAccountsTool extends MCPTool<GetAccountsToolInput> {
  name = "get_accounts";
  description = "Retrieves all open accounts for a given budget ID.";

  schema = {
    budgetId: {
      type: z.string().optional(),
      description: "The ID of the budget to retrieve accounts from. Defaults to YNAB_BUDGET_ID environment variable.",
    },
  };

  api: ynab.API;

  constructor() {
    super();
    this.api = new ynab.API(process.env.YNAB_API_TOKEN || "");
  }

  async execute(input: GetAccountsToolInput): Promise<Account[] | string> {
    if (!process.env.YNAB_API_TOKEN) {
        return "Error: YNAB_API_TOKEN environment variable is not set.";
    }
    const budgetId = input.budgetId || process.env.YNAB_BUDGET_ID;

    if (!budgetId) {
      return "Error: budgetId must be provided either as an input or as a YNAB_BUDGET_ID environment variable.";
    }

    try {
      logger.info(`Getting accounts for budget ${budgetId}`);
      const accountsResponse = await this.api.accounts.getAccounts(budgetId);
      const openAccounts = accountsResponse.data.accounts.filter(
        (account) => !account.closed && !account.deleted
      );

      logger.info(`Found ${openAccounts.length} open accounts`);

      return openAccounts.map(account => ({
        id: account.id,
        name: account.name,
        type: account.type,
        on_budget: account.on_budget,
        closed: account.closed,
        note: account.note,
        balance: account.balance,
        cleared_balance: account.cleared_balance,
        uncleared_balance: account.uncleared_balance,
        transfer_payee_id: account.transfer_payee_id,
        direct_import_linked: account.direct_import_linked,
        direct_import_in_error: account.direct_import_in_error,
        last_reconciled_at: account.last_reconciled_at,
        debt_original_balance: account.debt_original_balance,
        debt_interest_rates: account.debt_interest_rates,
        debt_minimum_payments: account.debt_minimum_payments,
        debt_escrow_amounts: account.debt_escrow_amounts,
        deleted: account.deleted,
      }));
    } catch (error) {
      logger.error(`Error getting accounts: ${JSON.stringify(error)}`);
      return `Error getting accounts: ${JSON.stringify(error)}`;
    }
  }
}

export default GetAccountsTool; 