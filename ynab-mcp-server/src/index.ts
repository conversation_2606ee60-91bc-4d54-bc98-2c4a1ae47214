import { MCPServer } from "mcp-framework";
import ListBudgetsTool from "./tools/ListBudgetsTool.js";
import BudgetSummaryTool from "./tools/BudgetSummaryTool.js";
import GetUnapprovedTransactionsTool from "./tools/GetUnapprovedTransactionsTool.js";
import CreateTransactionTool from "./tools/CreateTransactionTool.js";
import ApproveTransactionTool from "./tools/ApproveTransactionTool.js";
import GetAccountsTool from "./tools/GetAccountsTool.js";

const server = new MCPServer({
  transport: {
    type: "sse",
    options: {
      port: 3000,
    },
  },
});

server.start();

// Handle shutdown
process.on("SIGINT", async () => {
  await server.stop();
  process.exit(0);
});
