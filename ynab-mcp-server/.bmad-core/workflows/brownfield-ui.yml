workflow:
  id: brownfield-ui
  name: Brownfield UI/Frontend Enhancement
  description: >-
    Agent workflow for enhancing existing frontend applications with new features,
    modernization, or design improvements. Handles existing UI analysis and safe integration.
  type: brownfield
  project_types:
    - ui-modernization
    - framework-migration
    - design-refresh
    - frontend-enhancement

  # For Complex UI Enhancements (Multiple Stories, Design Changes)
  complex_enhancement_sequence:
    - step: scope_assessment
      agent: any
      action: assess complexity
      notes: "First, assess if this is a simple UI change (use simple_enhancement_sequence) or complex enhancement requiring full planning."

    - step: ui_analysis
      agent: analyst
      action: analyze existing UI
      notes: "Review existing frontend application, user feedback, analytics data, and identify improvement areas."

    - agent: pm
      creates: brownfield-prd.md
      uses: brownfield-prd-tmpl
      requires: existing_ui_analysis
      notes: "Creates comprehensive brownfield PRD focused on UI enhancement with existing system analysis. SAVE OUTPUT: Copy final brownfield-prd.md to your project's docs/ folder."

    - agent: ux-expert
      creates: front-end-spec.md
      uses: front-end-spec-tmpl
      requires: brownfield-prd.md
      notes: "Creates UI/UX specification for brownfield enhancement that integrates with existing design patterns. SAVE OUTPUT: Copy final front-end-spec.md to your project's docs/ folder."

    - agent: architect
      creates: brownfield-architecture.md
      uses: brownfield-architecture-tmpl
      requires:
        - brownfield-prd.md
        - front-end-spec.md
      notes: "Creates brownfield frontend architecture with component integration strategy and migration planning. SAVE OUTPUT: Copy final brownfield-architecture.md to your project's docs/ folder."

    - agent: po
      validates: all_artifacts
      uses: po-master-checklist
      notes: "Validates all brownfield documents for UI integration safety and design consistency. May require updates to any document."

    - agent: various
      updates: any_flagged_documents
      condition: po_checklist_issues
      notes: "If PO finds issues, return to relevant agent to fix and re-export updated documents to docs/ folder."

    - workflow_end:
      action: move_to_ide
      notes: "All planning artifacts complete. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  # For Simple UI Enhancements (1-3 Stories, Following Existing Design)
  simple_enhancement_sequence:
    - step: enhancement_type
      action: choose approach
      notes: "Choose between creating single story (simple component change) or epic (1-3 related UI changes)."

    - agent: pm|po|sm
      creates: brownfield_epic OR brownfield_story
      uses: brownfield-create-epic OR brownfield-create-story
      notes: "Create focused UI enhancement with existing design system integration. Choose agent based on team preference and context."

    - workflow_end:
      action: move_to_ide
      notes: "UI enhancement defined. Move to IDE environment to begin development. Explain to the user the IDE Development Workflow next steps: data#bmad-kb:IDE Development Workflow"

  flow_diagram: |
    ```mermaid
    graph TD
        A[Start: UI Enhancement] --> B{Enhancement Complexity?}
        B -->|Complex/Significant| C[analyst: analyze existing UI]
        B -->|Simple| D{1 Story or 2-3 Stories?}

        C --> E[pm: brownfield-prd.md]
        E --> F[ux-expert: front-end-spec.md]
        F --> G[architect: brownfield-architecture.md]
        G --> H[po: validate with po-master-checklist]
        H --> I{PO finds issues?}
        I -->|Yes| J[Return to relevant agent for fixes]
        I -->|No| K[Move to IDE Environment]
        J --> H

        D -->|1 Story| L[pm/po/sm: brownfield-create-story]
        D -->|2-3 Stories| M[pm/po/sm: brownfield-create-epic]
        L --> N[Move to IDE Environment]
        M --> N

        style K fill:#90EE90
        style N fill:#90EE90
        style E fill:#FFE4B5
        style F fill:#FFE4B5
        style G fill:#FFE4B5
        style L fill:#FFB6C1
        style M fill:#FFB6C1
    ```

  decision_guidance:
    use_complex_sequence_when:
      - UI enhancement requires multiple coordinated stories (4+)
      - Design system changes needed
      - New component patterns required
      - User research and testing needed
      - Multiple team members will work on related changes

    use_simple_sequence_when:
      - Enhancement can be completed in 1-3 stories
      - Follows existing design patterns exactly
      - Component changes are isolated
      - Risk to existing UI is low
      - Change maintains current user experience

  handoff_prompts:
    analyst_to_pm: "UI analysis complete. Create comprehensive brownfield PRD with UI integration strategy."
    pm_to_ux: "Brownfield PRD ready. Save it as docs/brownfield-prd.md, then create the UI/UX specification."
    ux_to_architect: "UI/UX spec complete. Save it as docs/front-end-spec.md, then create the frontend architecture."
    architect_to_po: "Architecture complete. Save it as docs/brownfield-architecture.md. Please validate all artifacts for UI integration safety."
    po_issues: "PO found issues with [document]. Please return to [agent] to fix and re-save the updated document."
    simple_to_ide: "UI enhancement defined with existing design integration. Move to IDE environment to begin development."
    complex_complete: "All brownfield planning artifacts validated and saved in docs/ folder. Move to IDE environment to begin development."
