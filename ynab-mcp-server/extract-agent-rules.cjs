const fs = require('fs');
const path = require('path');

const AGENTS_DIR = path.join(__dirname, '.bmad-core/agents');
const OUTPUT_DIR = path.join(__dirname, '.cursor/rules');

if (!fs.existsSync(OUTPUT_DIR)) fs.mkdirSync(OUTPUT_DIR, { recursive: true });

fs.readdirSync(AGENTS_DIR).forEach(file => {
  if (!file.endsWith('.md')) return;
  const content = fs.readFileSync(path.join(AGENTS_DIR, file), 'utf8');

  // Extract YAML block (between first two triple backticks with yaml)
  const yamlMatch = content.match(/```yaml([\s\S]*?)```/);
  const yamlBlock = yamlMatch ? yamlMatch[1].trim() : '';

  // Extract persona description (look for persona: ... until next top-level key or end)
  const personaMatch = content.match(/persona:\s*([\s\S]*?)(?=\n[a-zA-Z0-9_-]+:|\nstartup:|\ncommands:|\ndependencies:|\n$)/);
  const personaBlock = personaMatch ? personaMatch[1].trim() : '';

  // Compose output
  const out = [
    '---',
    yamlBlock,
    '---',
    '',
    '## Persona',
    '',
    personaBlock
  ].join('\n');

  // Write to .cursor/rules/ as .mdc
  const outFile = path.join(OUTPUT_DIR, file.replace('.md', '.mdc'));
  fs.writeFileSync(outFile, out, 'utf8');
  console.log(`Wrote: ${outFile}`);
}); 