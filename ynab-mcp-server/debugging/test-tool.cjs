const { EventSource } = require('eventsource');
const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

const sse = new EventSource(`${BASE_URL}/sse`);

sse.onopen = () => {
  console.log('SSE connection established.');
};

sse.onerror = (err) => {
  console.error('SSE error:', err);
  sse.close();
};

sse.onmessage = (event) => {
  console.log(`Received SSE message: ${event.data}`);
  try {
    const data = JSON.parse(event.data);
    if (data.result) {
        console.log('Tool result:', data.result);
        sse.close();
    }
  } catch (e) {
    // not json, just log it
  }
};

sse.addEventListener('endpoint', async (event) => {
  console.log('Received endpoint event:', event.data);
  const messageEndpoint = event.data;
  const url = new URL(`${BASE_URL}${messageEndpoint}`);
  const sessionId = url.searchParams.get('sessionId');

  if (!sessionId) {
    console.error('Could not extract sessionId from endpoint event.');
    sse.close();
    return;
  }
  
  console.log(`Using sessionId: ${sessionId}`);

  try {
    console.log('Making tool call...');
    const response = await axios.post(`${BASE_URL}/messages?sessionId=${sessionId}`, {
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: 'get_accounts',
        arguments: {},
      },
    }, { timeout: 10000 }); // 10 second timeout

    console.log('Tool call POST response status:', response.status);
    console.log('Tool call POST response data:', response.data);

  } catch (error) {
    console.error('Error calling tool:', error.response ? `${error.response.status} ${error.response.statusText}` : error.message);
  }
}); 