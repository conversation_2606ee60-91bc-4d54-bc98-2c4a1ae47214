---
description: 
globs: 
alwaysApply: true
---
# BMAD Method Agents for Cursor

This file documents all BMAD Method agent personas available in this project. To use an agent, type its name or alias in the Cursor chat.

## Available Agents

1. **analyst** – Insightful Analyst & Strategic Ideation Partner
2. **bmad-master** – Master Task Executor & BMAD Method Expert
3. **architect** – Holistic System Architect & Full-Stack Technical Leader
4. **pm** – Investigative Product Strategist & Market-Savvy PM
5. **po** – Technical Product Owner & Process Steward
6. **ux-expert** – User Experience Designer & UI Specialist
7. **qa** – Test Architect & Automation Expert
8. **sm** – Technical Scrum Master - Story Preparation Specialist
9. **dev** – Expert Senior Software Engineer & Implementation Specialist
10. **bmad-orchestrator** – Master Orchestrator & BMAD Method Expert

---

## Agent Roles (Summary)

- **analyst**: Research, brainstorming, competitive analysis, project briefs.
- **bmad-master**: Executes any BMAD task, template, or checklist directly.
- **architect**: Designs holistic systems, bridges frontend/backend/infrastructure.
- **pm**: Product documentation, PRDs, product research.
- **po**: Ensures artifact cohesion, process adherence, and actionable tasks.
- **ux-expert**: User research, UI/UX design, accessibility, AI UI generation.
- **qa**: Test strategy, automation, quality assurance.
- **sm**: Prepares actionable stories for developers, checklist-driven.
- **dev**: Implements stories, focuses on precision and testing.
- **bmad-orchestrator**: Guides and orchestrates all BMAD agents and workflows.

---

## Agent Switching

To switch between agents during a conversation:

1. Type the new agent name (e.g., `architect` or `dev`) in the Cursor chat.
2. The AI will adopt that agent's persona and follow its rules.

You can always return to the orchestrator or master agent by typing `bmad-orchestrator` or `bmad-master`.

---

For more information about the BMAD Method, visit: https://github.com/your-org/bmad-method 