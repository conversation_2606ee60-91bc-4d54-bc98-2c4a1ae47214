#!/usr/bin/env python3
"""
Unified Contact Manager
A comprehensive tool that combines cleaning, previewing, and organizing contacts
into one intuitive workflow.
"""

import os
import sys
import pandas as pd
import json
from datetime import datetime
from flask import Flask, render_template, jsonify, request, send_file
import tempfile
import shutil
import numpy as np

app = Flask(__name__)

class ContactManager:
    def __init__(self):
        self.workspace = os.path.dirname(os.path.abspath(__file__))
        self.backup_dir = os.path.join(os.path.dirname(self.workspace), 'augment')
        self.cleaned_dir = self.workspace
        
        # Ensure directories exist
        os.makedirs(self.backup_dir, exist_ok=True)
        os.makedirs(self.cleaned_dir, exist_ok=True)
        
        self.current_file = None
        self.contacts = []
        
    def get_available_files(self):
        """Get all available contact files."""
        files = {
            'original': [],
            'cleaned': []
        }
        
        # Original files in augment folder
        if os.path.exists(self.backup_dir):
            for file in os.listdir(self.backup_dir):
                if file.endswith('.csv'):
                    files['original'].append({
                        'name': file,
                        'path': os.path.join(self.backup_dir, file),
                        'size': os.path.getsize(os.path.join(self.backup_dir, file)),
                        'modified': datetime.fromtimestamp(os.path.getmtime(os.path.join(self.backup_dir, file)))
                    })
        
        # Cleaned files in cursor folder
        for file in os.listdir(self.cleaned_dir):
            if file.endswith('.csv') and file.startswith('cleaned_'):
                files['cleaned'].append({
                    'name': file,
                    'path': os.path.join(self.cleaned_dir, file),
                    'size': os.path.getsize(os.path.join(self.cleaned_dir, file)),
                    'modified': datetime.fromtimestamp(os.path.getmtime(os.path.join(self.cleaned_dir, file)))
                })
        
        return files
    
    def clean_contacts(self, input_file, output_prefix='cleaned_contacts'):
        """Clean contacts using the existing cleaner logic."""
        from contact_cleaner import ContactCleaner
        
        try:
            cleaner = ContactCleaner(input_file)
            stats = cleaner.clean_all_contacts()
            files_created = cleaner.export_contacts(output_prefix)
            
            # Convert numpy types to Python types for JSON serialization
            cleaned_stats = {}
            for key, value in stats.items():
                if isinstance(value, np.integer):
                    cleaned_stats[key] = int(value)
                elif isinstance(value, np.floating):
                    cleaned_stats[key] = float(value)
                elif isinstance(value, np.ndarray):
                    cleaned_stats[key] = value.tolist()
                else:
                    cleaned_stats[key] = value
            
            return {
                'success': True,
                'stats': cleaned_stats,
                'files': files_created
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def load_contacts(self, file_path):
        """Load contacts from a CSV file."""
        if not os.path.exists(file_path):
            return []
        
        df = pd.read_csv(file_path)
        contacts = []
        
        for idx, row in df.iterrows():
            first_name = row.get('First Name', '') if pd.notna(row.get('First Name')) else ''
            last_name = row.get('Last Name', '') if pd.notna(row.get('Last Name')) else ''
            organization = row.get('Organization Name', '') if pd.notna(row.get('Organization Name')) else ''
            
            # Build name
            name_parts = [first_name, last_name]
            full_name = ' '.join([part for part in name_parts if part.strip()])
            if not full_name.strip():
                full_name = organization if organization else "Unknown"
            
            # Get primary email and phone
            primary_email = ''
            primary_phone = ''
            
            for i in range(1, 5):
                email = row.get(f'E-mail {i} - Value', '')
                if pd.notna(email) and email.strip() and not primary_email:
                    primary_email = email.strip()
            
            for i in range(1, 4):
                phone = row.get(f'Phone {i} - Value', '')
                if pd.notna(phone) and phone.strip() and not primary_phone:
                    primary_phone = phone.strip()
            
            contacts.append({
                'id': idx,
                'name': full_name,
                'organization': organization,
                'email': primary_email,
                'phone': primary_phone,
                'first_name': first_name,
                'last_name': last_name,
                'row_data': row.to_dict()
            })
        
        return contacts

# Initialize contact manager
manager = ContactManager()

@app.route('/')
def index():
    """Main dashboard."""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Contact Manager - Unified Workflow</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; background: #f8f9fa; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header h1 { color: #1a73e8; margin-bottom: 10px; }
            .workflow { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
            .panel { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .panel h2 { color: #202124; margin-bottom: 15px; border-bottom: 2px solid #e8eaed; padding-bottom: 10px; }
            .file-list { max-height: 300px; overflow-y: auto; }
            .file-item { padding: 10px; border: 1px solid #e8eaed; margin: 5px 0; border-radius: 5px; cursor: pointer; transition: all 0.2s; }
            .file-item:hover { background: #f8f9fa; border-color: #1a73e8; }
            .file-item.selected { background: #e3f2fd; border-color: #1a73e8; }
            .file-info { font-size: 12px; color: #5f6368; margin-top: 5px; }
            .btn { padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; margin: 5px; }
            .btn-primary { background: #1a73e8; color: white; }
            .btn-success { background: #34a853; color: white; }
            .btn-danger { background: #ea4335; color: white; }
            .btn:disabled { background: #dadce0; cursor: not-allowed; }
            .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .contact-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
            .contact-card { background: white; padding: 15px; border-radius: 8px; border: 1px solid #e8eaed; }
            .contact-card.selected { border-color: #1a73e8; background: #e3f2fd; }
            .contact-name { font-weight: 600; font-size: 16px; margin-bottom: 5px; }
            .contact-org { color: #5f6368; font-size: 14px; margin-bottom: 8px; }
            .contact-details { font-size: 12px; color: #5f6368; }
            .checkbox { margin-right: 10px; }
            .loading { text-align: center; padding: 40px; color: #5f6368; }
            .error { color: #ea4335; padding: 10px; background: #fce8e6; border-radius: 5px; margin: 10px 0; }
            .success { color: #34a853; padding: 10px; background: #e6f4ea; border-radius: 5px; margin: 10px 0; }
            .tabs { display: flex; margin-bottom: 20px; }
            .tab { padding: 10px 20px; background: #f1f3f4; border: none; cursor: pointer; }
            .tab.active { background: #1a73e8; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📱 Contact Manager - Unified Workflow</h1>
                <p>Clean, preview, and organize your contacts in one place</p>
            </div>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('workflow')">🔄 Workflow</button>
                <button class="tab" onclick="showTab('preview')">👥 Preview</button>
                <button class="tab" onclick="showTab('manage')">⚙️ Manage</button>
            </div>
            
            <div id="workflow-tab">
                <div class="workflow">
                    <div class="panel">
                        <h2>📁 Original Files</h2>
                        <div id="original-files" class="file-list">Loading...</div>
                    </div>
                    
                    <div class="panel">
                        <h2>✨ Cleaned Files</h2>
                        <div id="cleaned-files" class="file-list">Loading...</div>
                    </div>
                </div>
                
                <div class="panel">
                    <h2>🔄 Cleaning Process</h2>
                    <div id="cleaning-status">Select an original file to start cleaning</div>
                    <button class="btn btn-primary" id="clean-btn" onclick="cleanSelectedFile()" disabled>Clean Selected File</button>
                    <div id="cleaning-results"></div>
                </div>
            </div>
            
            <div id="preview-tab" style="display: none;">
                <div class="panel">
                    <h2>👥 Contact Preview</h2>
                    <div class="stats" id="preview-stats">Select a file to preview contacts</div>
                    <div class="contact-grid" id="contact-grid"></div>
                </div>
            </div>
            
            <div id="manage-tab" style="display: none;">
                <div class="panel">
                    <h2>⚙️ File Management</h2>
                    <button class="btn btn-success" onclick="refreshFiles()">🔄 Refresh Files</button>
                    <button class="btn btn-danger" onclick="cleanupFiles()">🧹 Cleanup Old Files</button>
                    <div id="management-status"></div>
                </div>
            </div>
        </div>
        
        <script>
            let selectedOriginalFile = null;
            let selectedCleanedFile = null;
            let contacts = [];
            let selectedContacts = new Set();
            
            function showTab(tabName) {
                // Hide all tabs
                document.querySelectorAll('[id$="-tab"]').forEach(tab => tab.style.display = 'none');
                document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
                
                // Show selected tab
                document.getElementById(tabName + '-tab').style.display = 'block';
                event.target.classList.add('active');
                
                if (tabName === 'preview') {
                    loadFiles();
                }
            }
            
            function loadFiles() {
                fetch('/api/files')
                    .then(response => response.json())
                    .then(data => {
                        renderFileList('original-files', data.original, 'original');
                        renderFileList('cleaned-files', data.cleaned, 'cleaned');
                    });
            }
            
            function renderFileList(containerId, files, type) {
                const container = document.getElementById(containerId);
                container.innerHTML = files.map(file => `
                    <div class="file-item" onclick="selectFile('${type}', '${file.name}')">
                        <div><strong>${file.name}</strong></div>
                        <div class="file-info">
                            Size: ${(file.size / 1024).toFixed(1)}KB | 
                            Modified: ${new Date(file.modified).toLocaleDateString()}
                        </div>
                    </div>
                `).join('');
            }
            
            function selectFile(type, filename) {
                // Clear previous selections
                document.querySelectorAll('.file-item').forEach(item => item.classList.remove('selected'));
                
                // Select new file
                event.target.closest('.file-item').classList.add('selected');
                
                if (type === 'original') {
                    selectedOriginalFile = filename;
                    document.getElementById('clean-btn').disabled = false;
                    document.getElementById('cleaning-status').innerHTML = `Selected: <strong>${filename}</strong>`;
                } else if (type === 'cleaned') {
                    selectedCleanedFile = filename;
                    loadContacts(filename);
                }
            }
            
            function cleanSelectedFile() {
                if (!selectedOriginalFile) return;
                
                document.getElementById('cleaning-results').innerHTML = '<div class="loading">Cleaning contacts...</div>';
                
                fetch('/api/clean', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ filename: selectedOriginalFile })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('cleaning-results').innerHTML = `
                            <div class="success">
                                ✅ Cleaning completed!<br>
                                Original: ${data.stats.original_count} contacts<br>
                                Final: ${data.stats.total_contacts} contacts<br>
                                Personal: ${data.stats.personal_contacts} contacts<br>
                                Business: ${data.stats.business_contacts} contacts<br>
                                Files created: ${data.files.join(', ')}
                            </div>
                        `;
                        loadFiles(); // Refresh file lists
                    } else {
                        document.getElementById('cleaning-results').innerHTML = `
                            <div class="error">❌ Error: ${data.error}</div>
                        `;
                    }
                });
            }
            
            function loadContacts(filename) {
                fetch(`/api/contacts?file=${encodeURIComponent(filename)}`)
                    .then(response => response.json())
                    .then(data => {
                        contacts = data.contacts;
                        renderContacts();
                        updatePreviewStats();
                    });
            }
            
            function renderContacts() {
                const grid = document.getElementById('contact-grid');
                grid.innerHTML = contacts.map(contact => `
                    <div class="contact-card ${selectedContacts.has(contact.id) ? 'selected' : ''}" onclick="toggleContact(${contact.id})">
                        <input type="checkbox" class="checkbox" ${selectedContacts.has(contact.id) ? 'checked' : ''} onclick="event.stopPropagation()">
                        <div class="contact-name">${contact.name}</div>
                        ${contact.organization ? `<div class="contact-org">${contact.organization}</div>` : ''}
                        <div class="contact-details">
                            ${contact.email ? `📧 ${contact.email}` : ''}
                            ${contact.phone ? `📞 ${contact.phone}` : ''}
                        </div>
                    </div>
                `).join('');
            }
            
            function toggleContact(id) {
                if (selectedContacts.has(id)) {
                    selectedContacts.delete(id);
                } else {
                    selectedContacts.add(id);
                }
                renderContacts();
                updatePreviewStats();
            }
            
            function updatePreviewStats() {
                const withEmail = contacts.filter(c => c.email).length;
                const withPhone = contacts.filter(c => c.phone).length;
                const selected = selectedContacts.size;
                
                document.getElementById('preview-stats').innerHTML = 
                    `Total: ${contacts.length} | With Email: ${withEmail} | With Phone: ${withPhone} | Selected: ${selected}`;
            }
            
            function refreshFiles() {
                loadFiles();
                document.getElementById('management-status').innerHTML = '<div class="success">✅ Files refreshed</div>';
            }
            
            function cleanupFiles() {
                if (confirm('This will remove old backup files. Continue?')) {
                    fetch('/api/cleanup', { method: 'POST' })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                document.getElementById('management-status').innerHTML = 
                                    `<div class="success">✅ Cleanup completed. Removed ${data.removed} files.</div>`;
                                loadFiles();
                            } else {
                                document.getElementById('management-status').innerHTML = 
                                    `<div class="error">❌ Error: ${data.error}</div>`;
                            }
                        });
                }
            }
            
            // Load files on page load
            loadFiles();
        </script>
    </body>
    </html>
    '''

@app.route('/api/files')
def get_files():
    """Get all available files."""
    files = manager.get_available_files()
    return jsonify(files)

@app.route('/api/clean', methods=['POST'])
def clean_file():
    """Clean a selected file."""
    data = request.get_json()
    filename = data.get('filename')
    
    if not filename:
        return jsonify({'success': False, 'error': 'No filename provided'})
    
    input_path = os.path.join(manager.backup_dir, filename)
    if not os.path.exists(input_path):
        return jsonify({'success': False, 'error': 'File not found'})
    
    # Create backup
    backup_path = os.path.join(manager.backup_dir, f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}")
    shutil.copy2(input_path, backup_path)
    
    # Clean the file
    result = manager.clean_contacts(input_path)
    return jsonify(result)

@app.route('/api/contacts')
def get_contacts():
    """Get contacts from a specific file."""
    filename = request.args.get('file')
    if not filename:
        return jsonify({'contacts': []})
    
    file_path = os.path.join(manager.cleaned_dir, filename)
    if not os.path.exists(file_path):
        return jsonify({'contacts': []})
    
    contacts = manager.load_contacts(file_path)
    return jsonify({'contacts': contacts})

@app.route('/api/cleanup', methods=['POST'])
def cleanup_files():
    """Clean up old backup files."""
    try:
        removed = 0
        backup_dir = manager.backup_dir
        
        for file in os.listdir(backup_dir):
            if file.startswith('backup_') and file.endswith('.csv'):
                file_path = os.path.join(backup_dir, file)
                # Remove backups older than 7 days
                if (datetime.now().timestamp() - os.path.getmtime(file_path)) > 7 * 24 * 3600:
                    os.remove(file_path)
                    removed += 1
        
        return jsonify({'success': True, 'removed': removed})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 Starting Unified Contact Manager...")
    print("📱 Open your browser to: http://localhost:8080")
    print("🎯 This combines cleaning, previewing, and organizing in one workflow!")
    app.run(debug=True, host='0.0.0.0', port=8080) 