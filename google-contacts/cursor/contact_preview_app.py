#!/usr/bin/env python3
"""
Contact Preview Web App
A local web application to preview cleaned contacts as they would appear in Google Contacts
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import pandas as pd
import os
import json
from datetime import datetime
import re

app = Flask(__name__)

class ContactPreview:
    def __init__(self, csv_file):
        """Initialize with a CSV file of contacts."""
        self.df = pd.read_csv(csv_file)
        self.contacts = self.process_contacts()
    
    def clean_phone_display(self, phone):
        """Format phone number for display."""
        if pd.isna(phone) or phone == '':
            return ''
        
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', str(phone))
        
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            return str(phone)
    
    def get_contact_avatar(self, first_name, last_name):
        """Generate initials for avatar."""
        initials = ""
        if pd.notna(first_name) and first_name.strip():
            initials += first_name[0].upper()
        if pd.notna(last_name) and last_name.strip():
            initials += last_name[0].upper()
        return initials if initials else "?"
    
    def process_contacts(self):
        """Process contacts into a format suitable for display."""
        contacts = []
        
        for _, row in self.df.iterrows():
            # Get basic info
            first_name = row.get('First Name', '') if pd.notna(row.get('First Name')) else ''
            last_name = row.get('Last Name', '') if pd.notna(row.get('Last Name')) else ''
            middle_name = row.get('Middle Name', '') if pd.notna(row.get('Middle Name')) else ''
            organization = row.get('Organization Name', '') if pd.notna(row.get('Organization Name')) else ''
            title = row.get('Organization Title', '') if pd.notna(row.get('Organization Title')) else ''
            
            # Build full name
            name_parts = [first_name, middle_name, last_name]
            full_name = ' '.join([part for part in name_parts if part.strip()])
            if not full_name.strip():
                full_name = organization if organization else "Unknown Contact"
            
            # Get emails
            emails = []
            for i in range(1, 5):
                email_label = row.get(f'E-mail {i} - Label', '')
                email_value = row.get(f'E-mail {i} - Value', '')
                if pd.notna(email_value) and email_value.strip():
                    emails.append({
                        'label': email_label if pd.notna(email_label) else 'Other',
                        'value': email_value.strip()
                    })
            
            # Get phones
            phones = []
            for i in range(1, 4):
                phone_label = row.get(f'Phone {i} - Label', '')
                phone_value = row.get(f'Phone {i} - Value', '')
                if pd.notna(phone_value) and phone_value.strip():
                    phones.append({
                        'label': phone_label if pd.notna(phone_label) else 'Other',
                        'value': self.clean_phone_display(phone_value)
                    })
            
            # Get addresses
            addresses = []
            for i in range(1, 3):
                address_label = row.get(f'Address {i} - Label', '')
                address_formatted = row.get(f'Address {i} - Formatted', '')
                if pd.notna(address_formatted) and address_formatted.strip():
                    addresses.append({
                        'label': address_label if pd.notna(address_label) else 'Home',
                        'value': address_formatted.strip()
                    })
            
            # Get websites
            websites = []
            for i in range(1, 6):
                website_label = row.get(f'Website {i} - Label', '')
                website_value = row.get(f'Website {i} - Value', '')
                if pd.notna(website_value) and website_value.strip():
                    websites.append({
                        'label': website_label if pd.notna(website_label) else 'Other',
                        'value': website_value.strip()
                    })
            
            # Get birthday
            birthday = row.get('Birthday', '') if pd.notna(row.get('Birthday')) else ''
            
            # Get notes
            notes = row.get('Notes', '') if pd.notna(row.get('Notes')) else ''
            
            contact = {
                'id': len(contacts),
                'full_name': full_name,
                'first_name': first_name,
                'last_name': last_name,
                'middle_name': middle_name,
                'organization': organization,
                'title': title,
                'avatar': self.get_contact_avatar(first_name, last_name),
                'emails': emails,
                'phones': phones,
                'addresses': addresses,
                'websites': websites,
                'birthday': birthday,
                'notes': notes,
                'has_photo': pd.notna(row.get('Photo', '')) and row.get('Photo', '').strip() != '',
                'labels': row.get('Labels', '') if pd.notna(row.get('Labels')) else ''
            }
            
            contacts.append(contact)
        
        return contacts
    
    def search_contacts(self, query):
        """Search contacts by name, organization, or email."""
        if not query:
            return self.contacts
        
        query = query.lower()
        results = []
        
        for contact in self.contacts:
            # Search in name
            if query in contact['full_name'].lower():
                results.append(contact)
                continue
            
            # Search in organization
            if contact['organization'] and query in contact['organization'].lower():
                results.append(contact)
                continue
            
            # Search in emails
            for email in contact['emails']:
                if query in email['value'].lower():
                    results.append(contact)
                    break
            
            # Search in phones
            for phone in contact['phones']:
                if query in phone['value'].lower():
                    results.append(contact)
                    break
        
        return results

# Initialize contact preview
contact_preview = None

@app.route('/')
def index():
    """Main page."""
    return render_template('index.html')

@app.route('/api/contacts')
def get_contacts():
    """API endpoint to get contacts."""
    global contact_preview
    
    if contact_preview is None:
        # Try to load the personal contacts first
        csv_files = [
            'cleaned_contacts_personal.csv',
            'cleaned_contacts_all.csv',
            'cleaned_contacts_business.csv'
        ]
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                contact_preview = ContactPreview(csv_file)
                break
        
        if contact_preview is None:
            return jsonify({'error': 'No contact files found'}), 404
    
    search_query = request.args.get('search', '')
    contacts = contact_preview.search_contacts(search_query)
    
    return jsonify({
        'contacts': contacts,
        'total': len(contacts)
    })

@app.route('/api/contact/<int:contact_id>')
def get_contact(contact_id):
    """API endpoint to get a specific contact."""
    global contact_preview
    
    if contact_preview is None:
        return jsonify({'error': 'No contacts loaded'}), 404
    
    if contact_id < 0 or contact_id >= len(contact_preview.contacts):
        return jsonify({'error': 'Contact not found'}), 404
    
    return jsonify(contact_preview.contacts[contact_id])

@app.route('/api/load-file', methods=['POST'])
def load_file():
    """API endpoint to load a different contact file."""
    global contact_preview
    
    data = request.get_json()
    filename = data.get('filename')
    
    if not filename or not os.path.exists(filename):
        return jsonify({'error': 'File not found'}), 404
    
    try:
        contact_preview = ContactPreview(filename)
        return jsonify({
            'success': True,
            'total': len(contact_preview.contacts),
            'filename': filename
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/stats')
def get_stats():
    """API endpoint to get contact statistics."""
    global contact_preview
    
    if contact_preview is None:
        return jsonify({'error': 'No contacts loaded'}), 404
    
    contacts = contact_preview.contacts
    
    # Count contacts with different types of information
    stats = {
        'total': len(contacts),
        'with_emails': len([c for c in contacts if c['emails']]),
        'with_phones': len([c for c in contacts if c['phones']]),
        'with_addresses': len([c for c in contacts if c['addresses']]),
        'with_organizations': len([c for c in contacts if c['organization']]),
        'with_websites': len([c for c in contacts if c['websites']]),
        'with_birthdays': len([c for c in contacts if c['birthday']]),
        'with_photos': len([c for c in contacts if c['has_photo']])
    }
    
    return jsonify(stats)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Create the HTML template
    create_html_template()
    
    print("Starting Contact Preview App...")
    print("Open your browser to: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)

def create_html_template():
    """Create the HTML template for the web app."""
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Preview - Google Contacts Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Google Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #f8f9fa;
            color: #202124;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #dadce0;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            font-size: 22px;
            font-weight: 400;
            color: #202124;
        }
        
        .search-container {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .search-box {
            width: 400px;
            padding: 12px 16px;
            border: 1px solid #dadce0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        .search-box:focus {
            outline: none;
            border-color: #1a73e8;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }
        
        .file-selector {
            padding: 8px 12px;
            border: 1px solid #dadce0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }
        
        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .sidebar {
            width: 320px;
            background: white;
            border-right: 1px solid #dadce0;
            overflow-y: auto;
        }
        
        .contact-list {
            list-style: none;
        }
        
        .contact-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .contact-item:hover {
            background-color: #f8f9fa;
        }
        
        .contact-item.active {
            background-color: #e8f0fe;
            border-left: 3px solid #1a73e8;
        }
        
        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #1a73e8;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 16px;
            margin-right: 12px;
        }
        
        .contact-info {
            display: flex;
            align-items: center;
        }
        
        .contact-details h3 {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .contact-details p {
            font-size: 12px;
            color: #5f6368;
        }
        
        .content-area {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }
        
        .contact-detail-view {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .contact-header {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
        }
        
        .contact-header .contact-avatar {
            width: 80px;
            height: 80px;
            font-size: 32px;
            margin-right: 24px;
        }
        
        .contact-header-info h1 {
            font-size: 24px;
            font-weight: 400;
            margin-bottom: 4px;
        }
        
        .contact-header-info p {
            color: #5f6368;
            font-size: 14px;
        }
        
        .contact-section {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 500;
            color: #202124;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }
        
        .info-label {
            width: 80px;
            font-size: 12px;
            color: #5f6368;
            text-transform: uppercase;
            font-weight: 500;
        }
        
        .info-value {
            flex: 1;
            font-size: 14px;
        }
        
        .info-value a {
            color: #1a73e8;
            text-decoration: none;
        }
        
        .info-value a:hover {
            text-decoration: underline;
        }
        
        .stats-bar {
            background: white;
            border-bottom: 1px solid #dadce0;
            padding: 12px 24px;
            display: flex;
            gap: 24px;
            font-size: 12px;
            color: #5f6368;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .stat-number {
            font-weight: 500;
            color: #202124;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #5f6368;
        }
        
        .error {
            text-align: center;
            padding: 40px;
            color: #d93025;
        }
        
        .no-contacts {
            text-align: center;
            padding: 40px;
            color: #5f6368;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Contact Preview</h1>
        <div class="search-container">
            <select class="file-selector" id="fileSelector">
                <option value="cleaned_contacts_personal.csv">Personal Contacts</option>
                <option value="cleaned_contacts_all.csv">All Contacts</option>
                <option value="cleaned_contacts_business.csv">Business Contacts</option>
            </select>
            <input type="text" class="search-box" id="searchBox" placeholder="Search contacts...">
        </div>
    </div>
    
    <div class="stats-bar" id="statsBar">
        <div class="stat-item">
            <span>Total:</span>
            <span class="stat-number" id="totalContacts">0</span>
        </div>
        <div class="stat-item">
            <span>With Emails:</span>
            <span class="stat-number" id="withEmails">0</span>
        </div>
        <div class="stat-item">
            <span>With Phones:</span>
            <span class="stat-number" id="withPhones">0</span>
        </div>
        <div class="stat-item">
            <span>With Addresses:</span>
            <span class="stat-number" id="withAddresses">0</span>
        </div>
    </div>
    
    <div class="main-container">
        <div class="sidebar">
            <ul class="contact-list" id="contactList">
                <li class="loading">Loading contacts...</li>
            </ul>
        </div>
        
        <div class="content-area">
            <div class="contact-detail-view" id="contactDetail">
                <div class="no-contacts">Select a contact to view details</div>
            </div>
        </div>
    </div>

    <script>
        let contacts = [];
        let currentContact = null;
        
        // Load contacts on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadContacts();
            loadStats();
            
            // Set up event listeners
            document.getElementById('searchBox').addEventListener('input', debounce(handleSearch, 300));
            document.getElementById('fileSelector').addEventListener('change', handleFileChange);
        });
        
        function loadContacts() {
            fetch('/api/contacts')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showError(data.error);
                    } else {
                        contacts = data.contacts;
                        renderContactList();
                        if (contacts.length > 0) {
                            selectContact(contacts[0]);
                        }
                    }
                })
                .catch(error => {
                    showError('Failed to load contacts: ' + error.message);
                });
        }
        
        function loadStats() {
            fetch('/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (!data.error) {
                        document.getElementById('totalContacts').textContent = data.total;
                        document.getElementById('withEmails').textContent = data.with_emails;
                        document.getElementById('withPhones').textContent = data.with_phones;
                        document.getElementById('withAddresses').textContent = data.with_addresses;
                    }
                })
                .catch(error => {
                    console.error('Failed to load stats:', error);
                });
        }
        
        function handleFileChange() {
            const filename = document.getElementById('fileSelector').value;
            
            fetch('/api/load-file', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ filename: filename })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                } else {
                    loadContacts();
                    loadStats();
                }
            })
            .catch(error => {
                showError('Failed to load file: ' + error.message);
            });
        }
        
        function handleSearch() {
            const query = document.getElementById('searchBox').value;
            
            fetch(`/api/contacts?search=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showError(data.error);
                    } else {
                        contacts = data.contacts;
                        renderContactList();
                        if (contacts.length > 0) {
                            selectContact(contacts[0]);
                        } else {
                            showNoContacts();
                        }
                    }
                })
                .catch(error => {
                    showError('Search failed: ' + error.message);
                });
        }
        
        function renderContactList() {
            const contactList = document.getElementById('contactList');
            contactList.innerHTML = '';
            
            if (contacts.length === 0) {
                contactList.innerHTML = '<li class="no-contacts">No contacts found</li>';
                return;
            }
            
            contacts.forEach(contact => {
                const li = document.createElement('li');
                li.className = 'contact-item';
                li.onclick = () => selectContact(contact);
                
                const primaryEmail = contact.emails.length > 0 ? contact.emails[0].value : '';
                const primaryPhone = contact.phones.length > 0 ? contact.phones[0].value : '';
                
                li.innerHTML = `
                    <div class="contact-info">
                        <div class="contact-avatar">${contact.avatar}</div>
                        <div class="contact-details">
                            <h3>${contact.full_name}</h3>
                            <p>${primaryEmail || primaryPhone || contact.organization || ''}</p>
                        </div>
                    </div>
                `;
                
                contactList.appendChild(li);
            });
        }
        
        function selectContact(contact) {
            currentContact = contact;
            
            // Update active state in list
            document.querySelectorAll('.contact-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Find and highlight the selected contact
            const contactItems = document.querySelectorAll('.contact-item');
            const contactIndex = contacts.findIndex(c => c.id === contact.id);
            if (contactIndex >= 0 && contactItems[contactIndex]) {
                contactItems[contactIndex].classList.add('active');
            }
            
            renderContactDetail(contact);
        }
        
        function renderContactDetail(contact) {
            const detailView = document.getElementById('contactDetail');
            
            let detailHTML = `
                <div class="contact-header">
                    <div class="contact-avatar">${contact.avatar}</div>
                    <div class="contact-header-info">
                        <h1>${contact.full_name}</h1>
                        ${contact.organization ? `<p>${contact.organization}</p>` : ''}
                        ${contact.title ? `<p>${contact.title}</p>` : ''}
                    </div>
                </div>
            `;
            
            // Emails section
            if (contact.emails.length > 0) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Email</div>
                        ${contact.emails.map(email => `
                            <div class="info-item">
                                <div class="info-label">${email.label}</div>
                                <div class="info-value">
                                    <a href="mailto:${email.value}">${email.value}</a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // Phones section
            if (contact.phones.length > 0) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Phone</div>
                        ${contact.phones.map(phone => `
                            <div class="info-item">
                                <div class="info-label">${phone.label}</div>
                                <div class="info-value">
                                    <a href="tel:${phone.value.replace(/[^0-9+]/g, '')}">${phone.value}</a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // Addresses section
            if (contact.addresses.length > 0) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Address</div>
                        ${contact.addresses.map(address => `
                            <div class="info-item">
                                <div class="info-label">${address.label}</div>
                                <div class="info-value">${address.value}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // Websites section
            if (contact.websites.length > 0) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Website</div>
                        ${contact.websites.map(website => `
                            <div class="info-item">
                                <div class="info-label">${website.label}</div>
                                <div class="info-value">
                                    <a href="${website.value}" target="_blank">${website.value}</a>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // Birthday section
            if (contact.birthday) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Birthday</div>
                        <div class="info-item">
                            <div class="info-label">Birthday</div>
                            <div class="info-value">${contact.birthday}</div>
                        </div>
                    </div>
                `;
            }
            
            // Notes section
            if (contact.notes) {
                detailHTML += `
                    <div class="contact-section">
                        <div class="section-title">Notes</div>
                        <div class="info-item">
                            <div class="info-label">Notes</div>
                            <div class="info-value">${contact.notes}</div>
                        </div>
                    </div>
                `;
            }
            
            detailView.innerHTML = detailHTML;
        }
        
        function showError(message) {
            document.getElementById('contactList').innerHTML = `<li class="error">${message}</li>`;
            document.getElementById('contactDetail').innerHTML = `<div class="error">${message}</div>`;
        }
        
        function showNoContacts() {
            document.getElementById('contactDetail').innerHTML = '<div class="no-contacts">No contacts found</div>';
        }
        
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>'''
    
    with open('templates/index.html', 'w') as f:
        f.write(html_content) 