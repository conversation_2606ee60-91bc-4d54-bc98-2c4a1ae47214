#!/usr/bin/env python3
"""
Simple Contact Preview Web App with Selection/Deletion
A lightweight Flask app to preview and manage cleaned contacts
"""

from flask import Flask, render_template, jsonify, request
import pandas as pd
import os
import json

app = Flask(__name__)

def load_contacts(csv_file):
    """Load and process contacts from CSV."""
    if not os.path.exists(csv_file):
        return []
    
    df = pd.read_csv(csv_file)
    contacts = []
    
    for idx, row in df.iterrows():
        first_name = row.get('First Name', '') if pd.notna(row.get('First Name')) else ''
        last_name = row.get('Last Name', '') if pd.notna(row.get('Last Name')) else ''
        organization = row.get('Organization Name', '') if pd.notna(row.get('Organization Name')) else ''
        
        # Build name
        name_parts = [first_name, last_name]
        full_name = ' '.join([part for part in name_parts if part.strip()])
        if not full_name.strip():
            full_name = organization if organization else "Unknown"
        
        # Get primary email and phone
        primary_email = ''
        primary_phone = ''
        
        for i in range(1, 5):
            email = row.get(f'E-mail {i} - Value', '')
            if pd.notna(email) and email.strip() and not primary_email:
                primary_email = email.strip()
        
        for i in range(1, 4):
            phone = row.get(f'Phone {i} - Value', '')
            if pd.notna(phone) and phone.strip() and not primary_phone:
                primary_phone = phone.strip()
        
        contacts.append({
            'id': idx,
            'name': full_name,
            'organization': organization,
            'email': primary_email,
            'phone': primary_phone,
            'first_name': first_name,
            'last_name': last_name
        })
    
    return contacts

@app.route('/')
def index():
    """Main page."""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Contact Preview & Manager</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { margin-bottom: 20px; }
            .contact { border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 5px; display: flex; align-items: center; }
            .contact:hover { background-color: #f9f9f9; }
            .contact.selected { background-color: #e3f2fd; border-color: #2196f3; }
            .checkbox { margin-right: 10px; }
            .contact-info { flex: 1; }
            .name { font-weight: bold; font-size: 16px; }
            .org { color: #666; font-size: 14px; }
            .contact-details { margin-top: 5px; font-size: 12px; }
            .stats { background: #f5f5f5; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
            .actions { margin-bottom: 20px; }
            .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
            .btn-primary { background: #2196f3; color: white; }
            .btn-danger { background: #f44336; color: white; }
            .btn:disabled { background: #ccc; cursor: not-allowed; }
            .select-all { margin-bottom: 10px; }
            .file-selector { margin-bottom: 20px; }
            .file-selector select { padding: 8px; margin-right: 10px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Contact Preview & Manager</h1>
            <div class="file-selector">
                <select id="fileSelector" onchange="loadFile()">
                    <option value="cleaned_contacts_personal.csv">Personal Contacts</option>
                    <option value="cleaned_contacts_all.csv">All Contacts</option>
                    <option value="cleaned_contacts_business.csv">Business Contacts</option>
                </select>
            </div>
            <div class="stats" id="stats">Loading...</div>
            <div class="actions">
                <div class="select-all">
                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    <label for="selectAll">Select All</label>
                </div>
                <button class="btn btn-danger" id="deleteBtn" onclick="deleteSelected()" disabled>Delete Selected (<span id="selectedCount">0</span>)</button>
                <button class="btn btn-primary" onclick="exportSelected()">Export Selected</button>
            </div>
        </div>
        <div id="contacts">Loading contacts...</div>
        
        <script>
            let contacts = [];
            let selectedContacts = new Set();
            
            function loadContacts() {
                fetch('/api/contacts')
                    .then(response => response.json())
                    .then(data => {
                        contacts = data.contacts;
                        renderContacts();
                        updateStats();
                    });
            }
            
            function loadFile() {
                const filename = document.getElementById('fileSelector').value;
                fetch('/api/load-file', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ filename: filename })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadContacts();
                    }
                });
            }
            
            function renderContacts() {
                const contactsDiv = document.getElementById('contacts');
                contactsDiv.innerHTML = contacts.map(contact => `
                    <div class="contact ${selectedContacts.has(contact.id) ? 'selected' : ''}" onclick="toggleContact(${contact.id})">
                        <input type="checkbox" class="checkbox" ${selectedContacts.has(contact.id) ? 'checked' : ''} onclick="event.stopPropagation()">
                        <div class="contact-info">
                            <div class="name">${contact.name}</div>
                            ${contact.organization ? `<div class="org">${contact.organization}</div>` : ''}
                            <div class="contact-details">
                                ${contact.email ? `📧 ${contact.email}` : ''}
                                ${contact.phone ? `📞 ${contact.phone}` : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
            }
            
            function toggleContact(id) {
                if (selectedContacts.has(id)) {
                    selectedContacts.delete(id);
                } else {
                    selectedContacts.add(id);
                }
                updateSelection();
                renderContacts();
            }
            
            function toggleSelectAll() {
                const selectAll = document.getElementById('selectAll');
                if (selectAll.checked) {
                    contacts.forEach(contact => selectedContacts.add(contact.id));
                } else {
                    selectedContacts.clear();
                }
                updateSelection();
                renderContacts();
            }
            
            function updateSelection() {
                const deleteBtn = document.getElementById('deleteBtn');
                const selectedCount = document.getElementById('selectedCount');
                const count = selectedContacts.size;
                
                selectedCount.textContent = count;
                deleteBtn.disabled = count === 0;
            }
            
            function updateStats() {
                const withEmail = contacts.filter(c => c.email).length;
                const withPhone = contacts.filter(c => c.phone).length;
                const selected = selectedContacts.size;
                
                document.getElementById('stats').innerHTML = 
                    `Total: ${contacts.length} | With Email: ${withEmail} | With Phone: ${withPhone} | Selected: ${selected}`;
            }
            
            function deleteSelected() {
                if (selectedContacts.size === 0) return;
                
                if (confirm(`Are you sure you want to delete ${selectedContacts.size} contact(s)?`)) {
                    const selectedIds = Array.from(selectedContacts);
                    fetch('/api/delete-contacts', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ ids: selectedIds })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            selectedContacts.clear();
                            loadContacts();
                        } else {
                            alert('Error deleting contacts: ' + data.error);
                        }
                    });
                }
            }
            
            function exportSelected() {
                if (selectedContacts.size === 0) {
                    alert('Please select contacts to export');
                    return;
                }
                
                const selectedIds = Array.from(selectedContacts);
                fetch('/api/export-contacts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: selectedIds })
                })
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'selected_contacts.csv';
                    a.click();
                    window.URL.revokeObjectURL(url);
                });
            }
            
            // Load contacts on page load
            loadContacts();
        </script>
    </body>
    </html>
    '''

@app.route('/api/contacts')
def get_contacts():
    """API endpoint to get contacts."""
    # Try to load personal contacts first
    csv_files = [
        'cleaned_contacts_personal.csv',
        'cleaned_contacts_all.csv',
        'cleaned_contacts_business.csv'
    ]
    
    contacts = []
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            contacts = load_contacts(csv_file)
            break
    
    with_email = len([c for c in contacts if c['email']])
    with_phone = len([c for c in contacts if c['phone']])
    
    return jsonify({
        'contacts': contacts,
        'total': len(contacts),
        'with_email': with_email,
        'with_phone': with_phone
    })

@app.route('/api/load-file', methods=['POST'])
def load_file():
    """API endpoint to load a different contact file."""
    data = request.get_json()
    filename = data.get('filename')
    
    if not filename or not os.path.exists(filename):
        return jsonify({'success': False, 'error': 'File not found'})
    
    return jsonify({'success': True})

@app.route('/api/delete-contacts', methods=['POST'])
def delete_contacts():
    """API endpoint to delete selected contacts."""
    data = request.get_json()
    contact_ids = data.get('ids', [])
    
    if not contact_ids:
        return jsonify({'success': False, 'error': 'No contacts selected'})
    
    # Find the current CSV file
    csv_files = [
        'cleaned_contacts_personal.csv',
        'cleaned_contacts_all.csv',
        'cleaned_contacts_business.csv'
    ]
    
    current_file = None
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            current_file = csv_file
            break
    
    if not current_file:
        return jsonify({'success': False, 'error': 'No contact file found'})
    
    try:
        # Load current contacts
        df = pd.read_csv(current_file)
        
        # Remove selected contacts
        df = df.drop(contact_ids)
        
        # Save back to file
        df.to_csv(current_file, index=False)
        
        return jsonify({'success': True, 'deleted': len(contact_ids)})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/export-contacts', methods=['POST'])
def export_contacts():
    """API endpoint to export selected contacts."""
    from flask import send_file
    import tempfile
    
    data = request.get_json()
    contact_ids = data.get('ids', [])
    
    if not contact_ids:
        return jsonify({'error': 'No contacts selected'}), 400
    
    # Find the current CSV file
    csv_files = [
        'cleaned_contacts_personal.csv',
        'cleaned_contacts_all.csv',
        'cleaned_contacts_business.csv'
    ]
    
    current_file = None
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            current_file = csv_file
            break
    
    if not current_file:
        return jsonify({'error': 'No contact file found'}), 404
    
    try:
        # Load current contacts
        df = pd.read_csv(current_file)
        
        # Filter selected contacts
        selected_df = df.iloc[contact_ids]
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        selected_df.to_csv(temp_file.name, index=False)
        temp_file.close()
        
        return send_file(temp_file.name, as_attachment=True, download_name='selected_contacts.csv')
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("Starting Contact Preview & Manager App...")
    print("Open your browser to: http://localhost:8080")
    app.run(debug=True, host='0.0.0.0', port=8080) 