# Google Contacts Manager

A comprehensive tool for cleaning, analyzing, and organizing Google Contacts CSV exports with advanced filtering, grouping, and import capabilities.

## 🎯 Project Overview

This project evolved from a simple contact cleaning script into a full-featured contact management system. The goal was to create tools that help users:

1. **Clean and standardize** Google Contacts CSV exports
2. **Analyze contact data** with custom logic and filtering
3. **Group contacts** for organized import back to Google Contacts
4. **Filter contacts** based on available/empty fields
5. **Preview contacts** before import

## 📋 Features Implemented

### Core Functionality
- **Contact Cleaning**: Standardizes phone numbers, removes duplicates, cleans names
- **Business/Personal Separation**: Automatically categorizes contacts
- **Web Interface**: Flask-based UI for easy interaction
- **File Management**: Organized backup and cleaned file storage
- **Contact Preview**: Visual grid display of contacts with selection capabilities

### Advanced Analysis (Planned)
- **Custom Filtering Logic**: Filter by field availability, content patterns
- **Smart Grouping**: Create groups based on contact characteristics
- **Field Analysis**: Identify missing data, inconsistencies
- **Import Optimization**: Prepare contacts for Google Contacts import with groups

## 🏗️ Architecture

### Directory Structure
```
google-contacts/
├── augment/          # Original backup files
├── cursor/           # Cleaned and processed files
│   ├── contact_manager.py      # Main Flask application
│   ├── contact_cleaner.py      # Contact cleaning logic
│   └── requirements.txt        # Python dependencies
└── README.md         # This documentation
```

### Key Components

#### ContactCleaner Class
- Handles CSV parsing and cleaning
- Standardizes phone number formats
- Removes duplicate contacts
- Separates business from personal contacts
- Cleans multi-value fields

#### ContactManager Class
- Manages file operations
- Provides web API endpoints
- Handles contact loading and display
- Manages backup and cleanup operations

#### Web Interface
- Three-tab design: Workflow, Preview, Manage
- Real-time contact preview
- File selection and cleaning interface
- Contact selection and management

## 🔧 Technical Implementation

### Google Contacts CSV Format
- Supports standard Google Contacts export format
- Handles multi-value fields (separated by ` ::: `)
- Processes phone numbers, emails, addresses
- Manages group membership for import

### Group/Label Support
Google Contacts supports importing contacts with group information:
- Use `Group Membership` column in CSV
- Multiple groups separated by ` ::: ` (e.g., "Friends ::: Family")
- Groups are created automatically during import

### Data Processing
- Pandas for CSV manipulation
- Custom logic for contact categorization
- JSON serialization with numpy type handling
- Error handling for malformed data

## 🚀 Usage

### Prerequisites
```bash
pip3 install pandas flask
```

### Running the Application
```bash
cd cursor
python3 contact_manager.py
```

Access the web interface at: `http://localhost:8080`

### Workflow
1. **Upload/Select** original Google Contacts CSV
2. **Clean** contacts using automated processes
3. **Preview** cleaned contacts in web interface
4. **Select** contacts for further processing
5. **Export** cleaned files for import

## 🎯 Future Enhancements

### Custom Analysis Logic
- **Field-based filtering**: Filter by email presence, phone format, etc.
- **Pattern recognition**: Identify business vs personal contacts
- **Data quality scoring**: Rate contact completeness
- **Smart suggestions**: Suggest missing information

### Advanced Grouping
- **Automatic categorization**: Group by domain, location, frequency
- **Custom rules**: User-defined grouping criteria
- **Hierarchical groups**: Nested group structures
- **Import optimization**: Prepare optimal group structure

### Enhanced UI
- **Advanced filtering**: Multi-criteria contact filtering
- **Bulk operations**: Select and modify multiple contacts
- **Search functionality**: Find contacts by various criteria
- **Export options**: Multiple format support

## 🔍 Contact Analysis Features

### Field Analysis
- **Empty field detection**: Identify contacts missing key information
- **Format validation**: Check phone/email format consistency
- **Duplicate detection**: Find similar contacts
- **Data quality metrics**: Score contact completeness

### Filtering Capabilities
- **By field presence**: Show only contacts with/without specific fields
- **By content**: Filter by name patterns, domains, etc.
- **By category**: Business vs personal contacts
- **By quality**: High vs low quality contacts

### Grouping Logic
- **Domain-based**: Group by email domain
- **Geographic**: Group by area code or address
- **Frequency**: Group by contact interaction patterns
- **Custom**: User-defined grouping rules

## 📝 Development Notes

### Key Decisions
1. **Own Logic Implementation**: Chose to implement custom analysis rather than relying on external AI services
2. **Web Interface**: Flask-based UI for better user experience
3. **File Organization**: Separate directories for original vs processed files
4. **Modular Design**: Separate classes for different responsibilities

### Technical Challenges
- **JSON Serialization**: Handled numpy type conversion for API responses
- **CSV Parsing**: Robust handling of various Google Contacts export formats
- **File Management**: Organized backup and cleanup processes
- **Error Handling**: Graceful handling of malformed data

### Performance Considerations
- **Lazy Loading**: Load contacts only when needed
- **Efficient Filtering**: Use pandas for fast data operations
- **Memory Management**: Process large files in chunks
- **Caching**: Cache processed results for better performance

## 🤝 Contributing

This project is designed for personal use but can be extended for broader applications. Key areas for contribution:

- Enhanced filtering algorithms
- Additional export formats
- Improved UI/UX
- Performance optimizations
- Additional analysis features

## 📄 License

This project is for personal use and educational purposes.

---

*This project evolved from a simple contact cleaning need into a comprehensive contact management system, demonstrating the power of iterative development and user-driven feature requests.* 