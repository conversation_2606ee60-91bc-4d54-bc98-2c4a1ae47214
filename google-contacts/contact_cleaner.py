#!/usr/bin/env python3
"""
Google Contacts CSV Cleaner
Cleans up exported Google Contacts CSV files by:
- Standardizing phone number formats
- Removing duplicate contacts
- Cleaning up names and fields
- Separating business from personal contacts
- Removing system-generated entries
- Standardizing addresses
"""

import pandas as pd
import re
import numpy as np
from typing import Dict, List, Tuple, Optional
from collections import defaultdict
import argparse

class ContactCleaner:
    def __init__(self, csv_file: str):
        """Initialize the contact cleaner with a CSV file."""
        # Use more robust CSV reading with error handling
        try:
            self.df = pd.read_csv(csv_file, encoding='utf-8', on_bad_lines='skip')
        except:
            # Try with different encoding if UTF-8 fails
            try:
                self.df = pd.read_csv(csv_file, encoding='latin-1', on_bad_lines='skip')
            except:
                # Last resort - read with very permissive settings
                self.df = pd.read_csv(csv_file, encoding='utf-8', on_bad_lines='skip', 
                                    quoting=1, skipinitialspace=True)
        self.original_count = len(self.df)
        self.business_keywords = [
            'hospital', 'clinic', 'doctor', 'pharmacy', 'walmart', 'walgreens', 
            'att', 'sprint', 'usaa', 'ymca', 'auto', 'bank', 'church', 'school',
            'help', 'support', 'service', 'center', 'group', 'company', 'corp',
            'inc', 'llc', 'dental', 'medical', 'law', 'legal', 'repair', 'pest',
            'roofing', 'hvac', 'electric', 'plumbing', 'real estate', 'realtor'
        ]
        
    def clean_phone_number(self, phone: str) -> str:
        """Standardize phone number format to (XXX) XXX-XXXX."""
        if pd.isna(phone) or phone == '':
            return ''
        
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', str(phone))
        
        # Handle different lengths
        if len(digits) == 0:
            return ''
        elif len(digits) == 7:
            # Local number, assume area code needed
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 10:
            # Standard US number
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            # US number with country code
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
        else:
            # International or unusual format - return as is but cleaned
            return '+' + digits if not digits.startswith('1') else digits
    
    def clean_name_field(self, name: str) -> str:
        """Clean up name fields by removing extra descriptive text."""
        if pd.isna(name) or name == '':
            return ''
        
        name = str(name).strip()
        
        # Remove common descriptive additions
        patterns_to_remove = [
            r'\s+\(.*?\)',  # Remove parenthetical additions
            r'\s+\d{3}-\d{3}-\d{4}',  # Remove phone numbers in names
            r'\s+\d{10}',  # Remove 10-digit numbers
            r'\s+\+\d+',  # Remove phone numbers starting with +
            r'\s+\d+$',  # Remove trailing numbers
            r'\s+Apt\s+.*',  # Remove apartment info
            r'\s+\d+\s+(St|Ave|Rd|Dr|Ln|Ct|Way|Blvd)',  # Remove addresses
        ]
        
        for pattern in patterns_to_remove:
            name = re.sub(pattern, '', name, flags=re.IGNORECASE)
        
        # Remove extra whitespace
        name = ' '.join(name.split())
        
        return name
    
    def is_business_contact(self, row: pd.Series) -> bool:
        """Determine if a contact is a business based on various fields."""
        # Check organization name
        if pd.notna(row.get('Organization Name', '')) and str(row.get('Organization Name', '')).strip():
            return True
        
        # Check if name contains business keywords
        full_name = f"{row.get('First Name', '')} {row.get('Last Name', '')}".lower()
        for keyword in self.business_keywords:
            if keyword in full_name:
                return True
        
        # Check if it's a system contact
        system_indicators = ['outlook', 'contacts+', 'unroll.me', 'servicenow', 'uhg']
        for indicator in system_indicators:
            if indicator in full_name:
                return True
        
        return False
    
    def clean_multi_value_field(self, field: str) -> str:
        """Clean fields that contain multiple values separated by ' ::: '."""
        if pd.isna(field) or field == '':
            return ''
        
        # Split by ' ::: ' and take the first non-empty value
        values = str(field).split(' ::: ')
        for value in values:
            cleaned = value.strip()
            if cleaned and cleaned != '':
                return cleaned
        
        return ''
    
    def standardize_address(self, address: str) -> str:
        """Standardize address format."""
        if pd.isna(address) or address == '':
            return ''
        
        # Clean multi-value addresses
        address = self.clean_multi_value_field(address)
        
        # Basic address cleaning
        address = re.sub(r'\s+', ' ', address)  # Remove extra whitespace
        address = address.replace('\n', ', ')  # Replace newlines with commas
        
        return address.strip()
    
    def remove_duplicates(self) -> int:
        """Remove duplicate contacts based on phone numbers and names."""
        initial_count = len(self.df)
        
        # Create a key for duplicate detection
        self.df['duplicate_key'] = (
            self.df['First Name'].fillna('').str.lower() + '_' +
            self.df['Last Name'].fillna('').str.lower() + '_' +
            self.df['Phone 1 - Value'].fillna('')
        )
        
        # Remove exact duplicates
        self.df = self.df.drop_duplicates(subset=['duplicate_key'], keep='first')
        
        # Remove the helper column
        self.df = self.df.drop('duplicate_key', axis=1)
        
        removed_count = initial_count - len(self.df)
        return removed_count
    
    def clean_all_contacts(self) -> Dict[str, int]:
        """Apply all cleaning operations and return statistics."""
        stats = {}
        
        print("Starting contact cleaning process...")
        
        # Clean phone numbers
        phone_columns = [col for col in self.df.columns if 'Phone' in col and 'Value' in col]
        for col in phone_columns:
            self.df[col] = self.df[col].apply(self.clean_phone_number)
        
        # Clean names
        name_columns = ['First Name', 'Last Name', 'Middle Name', 'Nickname']
        for col in name_columns:
            if col in self.df.columns:
                self.df[col] = self.df[col].apply(self.clean_name_field)
        
        # Clean multi-value fields
        multi_value_columns = ['E-mail 1 - Value', 'E-mail 2 - Value', 'Address 1 - Formatted']
        for col in multi_value_columns:
            if col in self.df.columns:
                self.df[col] = self.df[col].apply(self.clean_multi_value_field)
        
        # Clean addresses
        address_columns = [col for col in self.df.columns if 'Address' in col and 'Formatted' in col]
        for col in address_columns:
            self.df[col] = self.df[col].apply(self.standardize_address)
        
        # Remove duplicates
        duplicates_removed = self.remove_duplicates()
        stats['duplicates_removed'] = duplicates_removed
        
        # Separate business and personal contacts
        self.df['is_business'] = self.df.apply(self.is_business_contact, axis=1)
        business_count = self.df['is_business'].sum()
        personal_count = len(self.df) - business_count
        
        stats['total_contacts'] = len(self.df)
        stats['business_contacts'] = business_count
        stats['personal_contacts'] = personal_count
        stats['original_count'] = self.original_count
        
        return stats
    
    def export_contacts(self, output_prefix: str = 'cleaned_contacts') -> List[str]:
        """Export cleaned contacts to separate files."""
        files_created = []
        
        # Remove the helper column before export
        export_df = self.df.drop('is_business', axis=1)
        
        # Export all cleaned contacts
        all_contacts_file = f"{output_prefix}_all.csv"
        export_df.to_csv(all_contacts_file, index=False)
        files_created.append(all_contacts_file)
        
        # Export personal contacts only
        personal_contacts = export_df[~self.df['is_business']]
        personal_file = f"{output_prefix}_personal.csv"
        personal_contacts.to_csv(personal_file, index=False)
        files_created.append(personal_file)
        
        # Export business contacts only
        business_contacts = export_df[self.df['is_business']]
        business_file = f"{output_prefix}_business.csv"
        business_contacts.to_csv(business_file, index=False)
        files_created.append(business_file)
        
        return files_created
    
    def print_statistics(self, stats: Dict[str, int]):
        """Print cleaning statistics."""
        print("\n" + "="*50)
        print("CONTACT CLEANING RESULTS")
        print("="*50)
        print(f"Original contacts: {stats['original_count']}")
        print(f"Final contacts: {stats['total_contacts']}")
        print(f"Duplicates removed: {stats['duplicates_removed']}")
        print(f"Personal contacts: {stats['personal_contacts']}")
        print(f"Business contacts: {stats['business_contacts']}")
        print(f"Reduction: {stats['original_count'] - stats['total_contacts']} contacts")
        print("="*50)


def main():
    parser = argparse.ArgumentParser(description='Clean Google Contacts CSV file')
    parser.add_argument('input_file', nargs='?', default='Google Contacts.csv',
                        help='Input CSV file (default: Google Contacts.csv)')
    parser.add_argument('--output', '-o', default='cleaned_contacts',
                        help='Output file prefix (default: cleaned_contacts)')
    
    args = parser.parse_args()
    
    try:
        # Initialize cleaner
        cleaner = ContactCleaner(args.input_file)
        
        # Clean contacts
        stats = cleaner.clean_all_contacts()
        
        # Export results
        print(f"\nExporting cleaned contacts...")
        files_created = cleaner.export_contacts(args.output)
        
        # Print results
        cleaner.print_statistics(stats)
        
        print(f"\nFiles created:")
        for file in files_created:
            print(f"  - {file}")
        
        print(f"\nCleaning complete! Ready for upload to Google Contacts and iCloud.")
        
    except FileNotFoundError:
        print(f"Error: Could not find input file '{args.input_file}'")
        print("Make sure the file exists in the current directory.")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main() 