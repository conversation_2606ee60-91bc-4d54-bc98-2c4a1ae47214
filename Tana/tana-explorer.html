<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tana Configuration Explorer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #2d3748;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .stat {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .controls {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .filter-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .filter-btn:hover {
            background: #f7fafc;
        }

        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
            align-items: start;
        }

        .sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            position: sticky;
            top: 2rem;
            max-height: calc(100vh - 4rem);
            overflow-y: auto;
        }

        .sidebar-header {
            background: #4a5568;
            color: white;
            padding: 1rem;
            font-weight: 600;
        }

        .sidebar-content {
            padding: 1rem;
        }

        .category {
            margin-bottom: 1rem;
        }

        .category-title {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.5rem;
            padding: 0.5rem;
            background: #f7fafc;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .category-items {
            margin-left: 1rem;
        }

        .sidebar-item {
            padding: 0.5rem;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.2s;
            font-size: 0.9rem;
        }

        .sidebar-item:hover {
            background: #edf2f7;
        }

        .sidebar-item.selected {
            background: #667eea;
            color: white;
        }

        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .content-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
            color: white;
            padding: 1.5rem;
        }

        .content-body {
            padding: 2rem;
        }

        .item-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .item-card {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            transition: all 0.3s;
            cursor: pointer;
        }

        .item-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .item-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2d3748;
        }

        .item-id {
            font-size: 0.8rem;
            color: #718096;
            font-family: monospace;
            margin-bottom: 0.5rem;
        }

        .item-description {
            color: #4a5568;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .item-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .meta-tag {
            background: #edf2f7;
            color: #4a5568;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }

        .children-count {
            background: #667eea;
            color: white;
        }

        .detail-view {
            display: none;
        }

        .detail-view.active {
            display: block;
        }

        .detail-header {
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .detail-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .detail-section {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .property-list {
            display: grid;
            gap: 0.5rem;
        }

        .property {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem;
            background: #f7fafc;
            border-radius: 4px;
        }

        .property-key {
            font-weight: 500;
            color: #4a5568;
        }

        .property-value {
            color: #2d3748;
            font-family: monospace;
            font-size: 0.9rem;
        }

        .back-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            margin-bottom: 1rem;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: #5a67d8;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #718096;
        }

        @media (max-width: 768px) {
            .content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                position: static;
                max-height: none;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Tana Configuration Explorer</h1>
        <div class="stats">
            <div class="stat">
                <strong id="total-docs">0</strong> Total Documents
            </div>
            <div class="stat">
                <strong id="total-supertags">0</strong> Supertags
            </div>
            <div class="stat">
                <strong id="total-fields">0</strong> Field Definitions
            </div>
            <div class="stat">
                <strong id="total-tuples">0</strong> Tuples
            </div>
        </div>
    </div>

    <div class="container">
        <div class="controls">
            <input type="text" class="search-box" placeholder="Search supertags, fields, or descriptions..." id="search-input">
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="tagDef">Supertags</button>
                <button class="filter-btn" data-filter="attrDef">Fields</button>
                <button class="filter-btn" data-filter="tuple">Tuples</button>
                <button class="filter-btn" data-filter="system">System</button>
                <button class="filter-btn" data-filter="user">User-Defined</button>
            </div>
        </div>

        <div class="content">
            <div class="sidebar">
                <div class="sidebar-header">
                    Categories
                </div>
                <div class="sidebar-content" id="sidebar-content">
                    <div class="loading">Loading categories...</div>
                </div>
            </div>

            <div class="main-content">
                <div class="content-header">
                    <h2 id="content-title">All Items</h2>
                    <p id="content-subtitle">Explore your Tana configuration</p>
                </div>
                <div class="content-body">
                    <div class="grid-view" id="grid-view">
                        <div class="loading">Loading Tana configuration...</div>
                    </div>
                    <div class="detail-view" id="detail-view">
                        <button class="back-btn" onclick="showGridView()">← Back to Grid</button>
                        <div id="detail-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let tanaData = null;
        let filteredData = [];
        let currentFilter = 'all';
        let currentSearch = '';
        let selectedItem = null;

        // Load and parse the Tana JSON file
        async function loadTanaData() {
            try {
                // First try to fetch the file
                const response = await fetch('Mehs.json');
                tanaData = await response.json();

                // Update stats
                updateStats();

                // Initialize the interface
                initializeInterface();

            } catch (error) {
                console.error('Error loading Tana data:', error);
                // Show file input fallback
                showFileInputFallback();
            }
        }

        function showFileInputFallback() {
            document.getElementById('grid-view').innerHTML = `
                <div style="text-align: center; padding: 3rem;">
                    <h3 style="color: #4a5568; margin-bottom: 1rem;">Load Your Tana Configuration</h3>
                    <p style="color: #718096; margin-bottom: 2rem;">
                        Due to browser security restrictions, please select your Mehs.json file:
                    </p>
                    <input type="file" id="file-input" accept=".json" style="
                        padding: 1rem;
                        border: 2px dashed #cbd5e0;
                        border-radius: 8px;
                        background: #f7fafc;
                        cursor: pointer;
                        font-size: 1rem;
                    ">
                    <p style="color: #a0aec0; font-size: 0.9rem; margin-top: 1rem;">
                        Your file will be processed locally in your browser - no data is uploaded anywhere.
                    </p>
                </div>
            `;

            document.getElementById('file-input').addEventListener('change', handleFileSelect);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            if (!file.name.endsWith('.json')) {
                alert('Please select a JSON file.');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    tanaData = JSON.parse(e.target.result);

                    // Update stats
                    updateStats();

                    // Initialize the interface
                    initializeInterface();

                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    document.getElementById('grid-view').innerHTML =
                        '<div class="loading">Error parsing JSON file. Please make sure it\'s a valid Tana export.</div>';
                }
            };

            reader.readAsText(file);
        }

        function updateStats() {
            if (!tanaData) return;
            
            const docs = tanaData.docs || [];
            const supertags = docs.filter(doc => doc.props._docType === 'tagDef');
            const fields = docs.filter(doc => doc.props._docType === 'attrDef');
            const tuples = docs.filter(doc => doc.props._docType === 'tuple');
            
            document.getElementById('total-docs').textContent = docs.length.toLocaleString();
            document.getElementById('total-supertags').textContent = supertags.length;
            document.getElementById('total-fields').textContent = fields.length;
            document.getElementById('total-tuples').textContent = tuples.length.toLocaleString();
        }

        function initializeInterface() {
            if (!tanaData) return;
            
            // Initialize sidebar
            initializeSidebar();
            
            // Initialize grid
            applyFilters();
            
            // Setup event listeners
            setupEventListeners();
        }

        function initializeSidebar() {
            const docs = tanaData.docs || [];
            const categories = {
                'System Supertags': docs.filter(doc => doc.props._docType === 'tagDef' && doc.id.startsWith('SYS_')),
                'User Supertags': docs.filter(doc => doc.props._docType === 'tagDef' && !doc.id.startsWith('SYS_')),
                'System Fields': docs.filter(doc => doc.props._docType === 'attrDef' && doc.id.startsWith('SYS_')),
                'User Fields': docs.filter(doc => doc.props._docType === 'attrDef' && !doc.id.startsWith('SYS_')),
                'Data Types': docs.filter(doc => doc.id.startsWith('SYS_D')),
                'System Values': docs.filter(doc => doc.id.startsWith('SYS_V')),
                'Tuples': docs.filter(doc => doc.props._docType === 'tuple').slice(0, 50) // Limit for performance
            };
            
            let sidebarHTML = '';
            for (const [categoryName, items] of Object.entries(categories)) {
                if (items.length === 0) continue;
                
                sidebarHTML += `
                    <div class="category">
                        <div class="category-title" onclick="toggleCategory('${categoryName}')">
                            ${categoryName} (${items.length})
                            <span>▼</span>
                        </div>
                        <div class="category-items" id="category-${categoryName.replace(/\s+/g, '-')}">
                            ${items.slice(0, 20).map(item => `
                                <div class="sidebar-item" onclick="selectItem('${item.id}')">
                                    ${item.props.name || item.id}
                                </div>
                            `).join('')}
                            ${items.length > 20 ? `<div class="sidebar-item" style="font-style: italic; color: #718096;">... and ${items.length - 20} more</div>` : ''}
                        </div>
                    </div>
                `;
            }
            
            document.getElementById('sidebar-content').innerHTML = sidebarHTML;
        }

        function setupEventListeners() {
            // Search input
            document.getElementById('search-input').addEventListener('input', (e) => {
                currentSearch = e.target.value.toLowerCase();
                applyFilters();
            });
            
            // Filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    currentFilter = e.target.dataset.filter;
                    applyFilters();
                });
            });
        }

        function applyFilters() {
            if (!tanaData) return;
            
            let docs = tanaData.docs || [];
            
            // Apply type filter
            if (currentFilter !== 'all') {
                if (currentFilter === 'system') {
                    docs = docs.filter(doc => doc.id.startsWith('SYS_'));
                } else if (currentFilter === 'user') {
                    docs = docs.filter(doc => !doc.id.startsWith('SYS_'));
                } else {
                    docs = docs.filter(doc => doc.props._docType === currentFilter);
                }
            }
            
            // Apply search filter
            if (currentSearch) {
                docs = docs.filter(doc => {
                    const name = (doc.props.name || '').toLowerCase();
                    const description = (doc.props.description || '').toLowerCase();
                    const id = doc.id.toLowerCase();
                    return name.includes(currentSearch) || 
                           description.includes(currentSearch) || 
                           id.includes(currentSearch);
                });
            }
            
            filteredData = docs;
            renderGrid();
        }

        function renderGrid() {
            const gridHTML = filteredData.map(item => {
                const hasChildren = item.children && item.children.length > 0;
                const docType = item.props._docType || 'unknown';
                const isSystem = item.id.startsWith('SYS_');
                
                return `
                    <div class="item-card" onclick="showDetail('${item.id}')">
                        <div class="item-title">${item.props.name || item.id}</div>
                        <div class="item-id">${item.id}</div>
                        ${item.props.description ? `<div class="item-description">${item.props.description}</div>` : ''}
                        <div class="item-meta">
                            <span class="meta-tag">${docType}</span>
                            ${isSystem ? '<span class="meta-tag">System</span>' : '<span class="meta-tag">User</span>'}
                            ${hasChildren ? `<span class="meta-tag children-count">${item.children.length} children</span>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('grid-view').innerHTML = gridHTML || '<div class="loading">No items match your criteria.</div>';
            
            // Update content header
            document.getElementById('content-title').textContent = 
                currentFilter === 'all' ? 'All Items' : 
                currentFilter === 'tagDef' ? 'Supertags' :
                currentFilter === 'attrDef' ? 'Field Definitions' :
                currentFilter === 'tuple' ? 'Tuples' :
                currentFilter === 'system' ? 'System Items' :
                currentFilter === 'user' ? 'User-Defined Items' : 'Items';
                
            document.getElementById('content-subtitle').textContent = 
                `${filteredData.length} items ${currentSearch ? `matching "${currentSearch}"` : ''}`;
        }

        function showDetail(itemId) {
            const item = tanaData.docs.find(doc => doc.id === itemId);
            if (!item) return;
            
            selectedItem = item;
            
            let detailHTML = `
                <div class="detail-header">
                    <div class="detail-title">${item.props.name || item.id}</div>
                    <div class="item-id">${item.id}</div>
                    ${item.props.description ? `<div class="item-description">${item.props.description}</div>` : ''}
                </div>
            `;
            
            // Properties section
            detailHTML += `
                <div class="detail-section">
                    <div class="section-title">Properties</div>
                    <div class="property-list">
            `;
            
            for (const [key, value] of Object.entries(item.props)) {
                if (key !== 'name' && key !== 'description') {
                    detailHTML += `
                        <div class="property">
                            <span class="property-key">${key}</span>
                            <span class="property-value">${JSON.stringify(value)}</span>
                        </div>
                    `;
                }
            }
            
            detailHTML += '</div></div>';
            
            // Children section
            if (item.children && item.children.length > 0) {
                detailHTML += `
                    <div class="detail-section">
                        <div class="section-title">Children (${item.children.length})</div>
                        <div class="property-list">
                `;
                
                item.children.forEach(childId => {
                    const childItem = tanaData.docs.find(doc => doc.id === childId);
                    const childName = childItem ? (childItem.props.name || childId) : childId;
                    detailHTML += `
                        <div class="property">
                            <span class="property-key">${childName}</span>
                            <span class="property-value">${childId}</span>
                        </div>
                    `;
                });
                
                detailHTML += '</div></div>';
            }
            
            document.getElementById('detail-content').innerHTML = detailHTML;
            document.getElementById('grid-view').style.display = 'none';
            document.getElementById('detail-view').classList.add('active');
        }

        function showGridView() {
            document.getElementById('detail-view').classList.remove('active');
            document.getElementById('grid-view').style.display = 'block';
        }

        function toggleCategory(categoryName) {
            const categoryId = 'category-' + categoryName.replace(/\s+/g, '-');
            const categoryElement = document.getElementById(categoryId);
            if (categoryElement) {
                categoryElement.style.display = categoryElement.style.display === 'none' ? 'block' : 'none';
            }
        }

        function selectItem(itemId) {
            // Remove previous selection
            document.querySelectorAll('.sidebar-item').forEach(item => item.classList.remove('selected'));
            
            // Add selection to clicked item
            event.target.classList.add('selected');
            
            // Show detail
            showDetail(itemId);
        }

        // Initialize the application
        loadTanaData();
    </script>
</body>
</html>
