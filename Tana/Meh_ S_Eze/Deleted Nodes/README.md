Deleted Nodes _(When a node is deleted, it is moved here)_
- H5
- **Entit**
- [e2UOp2zseOyD(Entity).md](<e2UOp2zseOyD(Entity).md>)
- **Project**
- [Physician Workgroup](<Physician Workgroup(H2S - Area).md>)
- Area
- **Project**
- #Project
- **Project**:
- **Entity**
- **Area**
- ****:
- **area**
- ****:
- Profession
- Family
- **Area**:
- Professional #H2 - Area 
- Prof
- Family
- [-j0Fsf-Xf5Mv(H2 - Area).md](<-j0Fsf-Xf5Mv(H2 - Area).md>)
- Prof
- UCSF
- Profess
- Professional #H2 - Area 
- Entity
- findFieldValues
  - **Area**
  - ABOVE
- findInstance
  - ABOVE
- Area
- 
- ****:
- ****: 
- **#task
AND (PARENT: @Tag**: "UCSF" OR GRANDPARENT: @Tag
- PARENT
- PARENT
  - 
- @Prof
- *2025-06-23*
- @Cut-Over
- [](<3FwtDxRhOgyj(Meeting).md>)
- @UCSF
- @UCSF
-  
-  
- @Professional
- @Personal
- Fire Prayers
  - [https://www.youtube.com/watch?v=ZlM55z5AkRc](https://www.youtube.com/watch?v=ZlM55z5AkRc)
- [i9ucU1pOA0aU(Meeting).md](<i9ucU1pOA0aU(Meeting).md>)
- @meeting
- @integrated
- UC
- Uc
- Reh
- Person
- @AIden
- Perso
- #Tags
- UCSF #Entity 
- UCSF #Entity 
- U
- tags
- [](<Vc-0kTcXSaM_(Meeting).md>)
- @Tonia
- @physician
- @moved
- @af
- And before we exit the outpatient version, was there any other questions about the outpatient workflow?
- So there's that. And now I will pass this over to Katie Hughes to do an inpatient version.
- All right. And you can see that we haven't updated here. And then Allison has final verified it. And here is what shows up in the results.
- Copy to the bottom so we can see this whole bar better, right? So you can see all these columns with details about what has actually happened. So far, for these orders, and Allison, have you wrapped up this whole thing? If I were to like refresh what I just did, you should see it when you refresh.
- And this just gives you a high-level look at what is actually happened with your specimens up to this point.
- This is not a lab-only activity. You see the word lab up here.
- So okay. So here we are in chart review.
- When you say scan the patient? Yeah. Wait. Sorry. Not peripheral. Not for ambulatory. So Katie for inpatient, they're scanning the patient. Or armband, but outpatient, they're not going to have the armband. So I'm out. So an outpatient when we're talking about scanning, we're meaning scanning the barcode on the label of each of the tubes that you collected. That's how you document your collection.
- Specifically, you'll do the patient and you'll do the label you just put on that container.
- Okay.
- A lab label.
- It will be a lab barcode.
- what were they scanning?
- For the ambulatory setting,
- It's only a click to simulate the scan.
- Kevin, yeah, let's what's your question?
- And then really quick, Allison is resulting this in the background. And I am going to pull up chart review. And while I do that,
- a pick a line if you have one that you did a peripheral cross and a pick line want to make sure that you have that source set correctly.
- And one other thing to make sure, especially on blood, that there's source is correct. It may default to, let's say,
- Based on, I believe, that if folks need to write a comment that they normally would have written on the label manually, they want to use those add that comment type of link. Sorry. Just that part.
- Excellent. One call out. I remember this from the training, which is for everyone's benefit here.
- an analysis resulting in the background.
- So Nick,
- And now I accept. And if I want to reprint labels, you can see here there's an option for that.
- So now I'm going to go click here to document collection details. Typically, you would scan with a label or scan with a barcode. And you know that will be your normal workflow. If you can help it, don't click to scan. There are reports that you know check audit trails for that to make sure we can have scanners. Up here, briefly, just call out these are going to be the defaults that will pull in when I do this. So I click. All right. You see a change color. I click here. You see a change color.
- Like, you know, and then here's our typical CPID. This is what you want to look for to make sure that accession can be here is going to be an ID structure that resembles this.
- We'll do something called case building. That's not part of today's specimen collection demo. But then you will see like a 25 SF dash type number.
- I just want to add one more. I don't think I mean, one second, Kevin. And if folks, any questions there.
- Okay. I just want to point out that I don't see the obvious, but so the the staff has collected the specimen and documented the collection. Then there's a piece in there in the middle that we want to be like love and think about. Um, which is how do you get the specimens to the lab, right? So we're going to focus on it will be whether the carriers be on this book of this discussion, which is kind of keep that in mind. Then by the time the specimen gets to the lab, that's the part that Allison just kind of worked on, which is the resulting piece.
- [AI] Process Known Person
  - [Ask AI (non-streaming)](https://app.tana.inc/?nodeid=SYS_M001)
  - [Text processing agent](https://app.tana.inc/?nodeid=SYS_M031)
  - ****:
- ****:
- ****:
- **No**
- **Text**
- ****:
- ****:
- @ex
- @ask
- @Ask
- Sister
- Family
- Family
- ****:
- Family
- Son
- Family
- Fai
- Family
- [J6FTFs5xaBu8(Relationship).md](<J6FTFs5xaBu8(Relationship).md>)
- [2025-06-16 - Monday](<2025-06-16 - Monday(Day)/README.md>)
- 1fr6ovCD0Oky
- S
- Re
- Context
- gWG94bf6Hj7T
- [Qk1q6BHSBi_Y(Meeting).md](<Qk1q6BHSBi_Y(Meeting).md>)
- CLB8cp7rVsLk
- [Join meeting](https://ucsf.zoom.us/j/92479835732?pwd=3mwYQ6zKoDgRcAlFWFEJbSby9xLQkw.1&from=addon)