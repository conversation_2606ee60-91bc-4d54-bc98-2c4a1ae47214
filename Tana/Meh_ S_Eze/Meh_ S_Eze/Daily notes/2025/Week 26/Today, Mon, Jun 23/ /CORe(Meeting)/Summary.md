Summary
- Integrated IT Manager Checklist
  - Review and update tasks in the integrated IT manager checklist (Core).
    - Each department team updates their section’s ownership and marks tasks as NA or with priority.
      - 120/90-day items due by 6/27.
      - 60/30/15-day items due by 7/11.
    - Process for adding new tasks:
      - Analysts propose additions to Speaker 2 first.
      - New rows should be flagged (e.g., "new") so others can review applicability.
- Graph Creation for Checklist Tracker
  - Leadership request to convert the tracker into a graphical dashboard (similar to the revenue cycle tracker).
  - Speaker 1 to engage <PERSON><PERSON><PERSON>:
    - Graph will take 2–3 hours to build.
    - <PERSON><PERSON><PERSON> has expertise to streamline creation.
- Analyst Responsibilities & Communication
  - Analysts will:
    - Mark tasks as complete once end users finish their work.
    - Track progress on items (e.g., specimen label stock procurement).
  - Speaker 2 to send follow-up emails after Core meetings:
    - Distribute slide decks and agendas.
    - Remind managers to update tracker items (e.g., tasks 12–14).
- Core Playbook Review
  - The Core playbook (Epic system readiness tasks due by 8/1) is an internal roadmap:
    - Many items have been completed in collaboration with Epic representative.
    - No immediate end-user checks needed; review progress in working meetings.
  - A previously created graph lacks follow-up; monitor if stakeholders revisit it.
- Manager Readiness Checklist
  - No changes were made to the manager readiness checklist during this meeting.
  - <PERSON><PERSON> and <PERSON> 2 maintain a simplified version for clarity and will sync updates manually.
- Change Impact Tracker & Advisory Councils
  - Review the change impact tracker to identify items for presentation at Core vs. advisory councils:
    - Core: Broad nursing and ancillary topics (e.g., specimen collection workflows).
    - Physician Advisory Council: Physician-facing changes without Core counterpart.
    - Outpatient Council: Ambulatory or outpatient-specific items if identified.
  - Process:
    - Present proposed changes at Core first.
    - If objections arise or topic is specialized, escalate to the appropriate advisory council.
- Readiness Presentations & Slides
  - Utilize Epic’s readiness tracker slide templates for upcoming Core presentations.
  - Adjust the target audience in the slide master (e.g., change "clinical advisory council" to "Core").
  - Select only broad-impact items that apply across departments; exclude highly specialized topics (e.g., pharmacy dispensing).
- Core Meeting Planning & Agendas
  - Meeting cadence: 30-minute working sessions (weekly or bi-weekly).
  - Source agenda topics from:
    - Readiness tracker presentations.
    - High-impact change tracker entries.
  - Goal: stay ahead by assigning analysts presentation topics with sufficient lead time.
  - Proposed topics for next Core meeting (7/3):
    - Code status orders.
    - Order set cleanup (e.g., progressive mobility, bladder protocol).
- Next Steps and Action Items
  - Speaker 2:
    - Send email to analysts with update deadlines (6/27 and 7/11).
    - Coordinate with analysts on tracker updates and new task proposals.
    - Plan next working meeting to review departmental progress and finalize Core meeting agendas.
  - Both:
    - Review high-impact change tracker to tag items for the 6/1 and 7/3 Core meetings.
    - Finalize agenda and slide assignments for upcoming sessions.
    - Monitor completion of internal Core playbook tasks and follow up on outstanding graphs.