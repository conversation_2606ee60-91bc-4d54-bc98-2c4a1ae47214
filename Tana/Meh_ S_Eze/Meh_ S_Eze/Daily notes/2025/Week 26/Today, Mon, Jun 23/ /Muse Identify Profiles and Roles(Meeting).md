Muse Identify Profiles and Roles #Meeting
- **Area**: Muse #H2 - Area
- **Entity**: UCSF #Entity
- **Action items**:
  - Create a St. Mary’s and <PERSON><PERSON> Francis profile in Muse and work with <PERSON> and <PERSON> to set it up *
  - Check if <PERSON> can join the meeting *
- Summary
  - Context and Introductions
    - <PERSON> joins as a new member of the orders team; will take time to ramp up.
    - Core participants: <PERSON> (project lead), <PERSON><PERSON> (Muse system specialist), <PERSON> (EKG processor).
  - Role and Profile Configuration Discussion
    - GE recommended using existing Muse roles and simply adding St. Mary’s and St. Francis locations to those profiles.
    - Concern raised about ensuring data segregation so each team sees only their own studies and avoids processing overlaps.
  - Proposed Workflow Solutions
    - Maintain current roles (EKG techs, physicians, etc.) without creating new profiles.
    - Create a dedicated St. Mary’s/St. Francis in-basket (or folder) in Muse:
      - Route their EKG carts exclusively to that in-basket.
      - Allow the St. Mary’s/St. Francis team to process only their studies.
      - Prevent Robert’s team from handling cleanup of unfamiliar data (e.g., <PERSON> monitor issues).
    - Leverage cart-location routing and profile views to filter by assigned location.
    - Plan a round of testing in Muse to validate the routing and view configuration.
  - Next Steps and Timeline
    - <PERSON> to draft and configure a test profile; collaborate with <PERSON><PERSON> and <PERSON> on setup.
    - Convene a follow-up session to review test results and finalize configuration.
    - Monet will remain available remotely through September to support transition.
    - Longer-term: consider full integration of both organizations into one workflow once systems are aligned.
- **Project**: Muse Cart Configuration #Project