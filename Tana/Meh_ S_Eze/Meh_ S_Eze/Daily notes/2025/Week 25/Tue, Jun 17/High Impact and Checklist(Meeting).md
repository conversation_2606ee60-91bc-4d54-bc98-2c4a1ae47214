High Impact and Checklist #Meeting
- **Date**: *2025-06-17T13:00:00.000[America/Chicago]/2025-06-17T14:00:00.000[America/Chicago]*
- **Action items**:
  - Create a personal simplified version of the readiness checklist *
  - Print out orders for EEG and PFT *
  - Add line in order transmittal for PFT to support non-interfaced orders *
  - Update interface table with new console orders entries *
  - Send the console orders file to the team *
  - Email <PERSON> confirming review of the spreadsheet and next steps *
  - Review <PERSON>’s email and provide updates to the team *
  - Meet with providers and nursing next week to review clinical protocols *
  - Send a poll to finalize the provider meeting schedule *
  - Share the calendar invite for this meeting *
  - Copy and send the meeting link to <PERSON> *
  - Review existing quick lists in the shared spreadsheet *
- Summary
  - Content Management Ticket & Order Set Review
    - Access & Context
      - Speaker 2 initially unable to access Content Management due to security settings; resolved with Speaker 4’s assistance.
      - Reviewing ED order sets created by <PERSON> to ensure correct IDs and items are captured.
    - Order Set & Quick List Clarifications
      - Distinction between Preference Lists (PRLs), quick lists (under profiles/LPRs), and order sets; confirmed quick lists reside in profiles, not preference lists.
      - Discussion on naming conventions: “order sets” vs. “ordersets,” smart groups formatting, and Epic terminology updates.
    - Location-Specific Records & IDs
      - Identification of existing LPF/PRL records for PARN (PARNassus), SF (<PERSON>. <PERSON>), SM (St. <PERSON>’s) with unique IDs; <PERSON> intends to duplicate and tailor PARN lists for each location.
      - Speaker 4 to send console order list (highlighting new EAPs) for <PERSON> to import into the appropriate quick lists.
    - Sign-off Process
      - <PERSON> to meet with providers (Tues–Thurs) and nursing (standing Wed work group) next week for detailed review and sign-off on order sets and quick lists.
      - <PERSON> to update Katie, <PERSON>, and Shem on outcomes and any outstanding clinical review items (e.g., medication status).
  - Manager Readiness & Change Impact Tracker
    - Manager Readiness Worksheet
      - Confirmed no new items need adding; team will follow up with managers of EED, PFT, infection control, and ECG for progress updates.
      - Plan to create a simplified personal checklist to track completion where the shared spreadsheet’s check-off functionality is limited.
    - Change Impact Tracker Review
      - Reviewed high-impact changes and operational owners:
        - Blood ordering & administration: high impact for nursing and anesthesia—already flagged.
        - Advanced Care Planning orders & navigator: assigned to Leo, with site-specific resident vs. attendee versions.
        - Critical values & lab changes: pending clarification from Katie on intended scope.
      - Confirmed other workflows (discharge/readmit, LOA) are unchanged for these departments or currently in place at UCSF #Entity and partner facilities.
    - Training & Go-Live Preparation
      - Day-One Activities: identify super users, assign training tracks, ensure hardware/printer availability for orders.
      - Clarified that appointment conversion and patient chart conversion are managed elsewhere; not a direct responsibility of this group.
  - Actions & Next Steps
    - Speaker 4 (Tanya)
      - Share link to final console order list with new EAPs highlighted.
      - Send any console orders filtered for St. Francis and St. Mary’s.
    - Dean (Speaker 5)
      - Import shared EAPs into PARN quick list, duplicate for SF and SM as needed.
      - Meet with providers and nursing next week; gather sign-off and report back to Shem, Katie, and Tanya.
    - Shem (Speaker 2)
      - Email Katie to confirm squad reviewed the Change Impact Tracker; request any additional high-impact items from Leo.
      - Finalize personal checklist for manager readiness follow-up.
    - All
      - Continue to monitor for ad-hoc changes post-review and address any new issues as they arise prior to go-live.