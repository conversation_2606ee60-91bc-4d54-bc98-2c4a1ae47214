Rehab Pt Class #Meeting
- **Date**: *2025-06-18T15:00:00.000[America/Chicago]/2025-06-18T15:15:00.000[America/Chicago]*
- **Action items**:
  - Update UC patient flow matrix to reflect agreed workflow *
  - Create RITM for AC3 detailing the build requirements for updating rehab order sets *
  - Draft the RITM summary and verify verbiage with <PERSON><PERSON> *
  - Update the patient movement physio diagrams to reflect the new rehab discharge-readmit workflow *
- Summary
  - Workflow Overview
    - Goal: Implement a dedicated discharge‐readmit workflow for inpatient rehab transfers (e.g., from UCSF/Parnassus to St. Mary St. Francis Rehab)
    - Model off existing Psych discharge‐readmit process using a pending admission and signed‐and‐held admit orders
  - Current Challenges
    - Oakland Mixed‐Use Unit
      - Inconsistent use of “Admit to Inpatient Rehab” orders; many patients remain classified as general inpatient
      - Rehab addendum order sets lack the actual admit‐to‐rehab ADT order
      - Existing internal workflow in Epic not executed reliably, producing missing orders on new encounters
    - West Bay Inpatient Rehab Beds
      - Patients in UCSF’s own rehab beds incorrectly admitted as general inpatient
    - St. Mary St. Francis Referral Process
      - External referrals arrive via CarePort/third‐party systems
      - Transfer Center manually creates pending admissions; no Epic order to auto‐trigger intake
  - Proposed Workflow and Orders
    - Leverage Psych Discharge‐Readmit Model
      - Use Discharge Readmit Navigator tab on the pending‐admission encounter to document orders
      - Place and hold the “Admit to Inpatient Rehab” ADT order in the rehab addendum, then release when patient arrives
    - Transfer Center Intake Integration
      - Continue manual creation of transfer requests upon referral
      - Evaluate future option for an Epic order to auto‐generate the intake request for internal transfers
    - Order Set Configuration
      - Build a dedicated Rehab Addendum order set including:
        - Signed‐and‐held ADT “Admit to Inpatient Rehab” order
        - Patient class filters ensuring correct classification
      - Merge Core Admission and Rehab Addendum order sets:
        - Internal readmits (UCSF #Entity → SFSM): Rehab addendum only
        - External admits (e.g., Stanford → SFSM): Core admission + Rehab addendum
  - Additional Considerations
    - Pediatric vs. Adult Rehab
      - Existing pediatric addendum hidden for Oakland; may require new adult/pediatric rehab addenda
    - Phase of Care & VCN Settings
      - Verify that Discharge‐Readmit phase allows code status and Med Reconciliation flags per site requirements
    - Handling Multiple Referral Sources
      - Workflow must support referrals from diverse hospitals without over‐automation
    - Temporary Manual Processes
      - Manual transfer creation continues until auto‐order solution is approved and built
  - Next Steps
    - ​1. Update UCSF #Entity Patient Movement Matrix to reflect agreed rehab readmit workflow
    - ​2. Draft RITM(s) for AC3 build requests:
      - Modify existing order sets (add admit‐to‐rehab order, patient class filters)
      - Restrict or require addendum per department/encounter needs
    - ​3. Present documented workflow to St. Mary St. Francis stakeholders for sign‐off or change requests
    - ​4. Update process flow diagrams (physios) and link them in the patient movement matrix
    - ​5. Conduct build testing:
      - Validate merged order set sequencing and ADT order behavior
      - Confirm VCN review requirements for code status and Med Rec orders
    - ​6. Schedule follow‐up meeting to address three‐day readmit HAR‐merge vs. new admission functionality
- **General Tags**:
  - UCSF #Entity
  - Rehab #Tags
- **Area**: Professional #H2 - Area