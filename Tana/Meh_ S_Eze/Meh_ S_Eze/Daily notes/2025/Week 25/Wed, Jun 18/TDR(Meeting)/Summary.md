Summary
- Purpose and Scope
  - Coordinate the end-to-end print mapping effort for the upcoming TDR (Transmittal of Digital Records) project
  - Ensure server-side record builds (EPR, LWS) and workstation-side print mappings are completed in a timely, sequenced, and coordinated manner
- Roles and Responsibilities
  - <PERSON> (Server Build Lead)
    - Completed EPR and LWS records for most PCs and printers
    - Will finish special configurations for 4 Epson label printers and 2 pharmacy receipt printers
    - Builds EPS print queues (with drivers) as each printer comes online
  - Shim (Print Mapping Lead)
    - Maps printer classifications (VLP, VLP alternatives) at system and workstation levels
    - Coordinates LOM (Location of Master) mapping for inpatient units, ED, and bedded units
    - Works with <PERSON> (OTX component) and <PERSON> (Optime component) to understand end-to-end printing requirements
  - <PERSON> <PERSON> (Project Oversight)
    - Identified need to form this cross-functional group
    - Provided the master list of workstations and printers by location (8 East, 8 West, etc.)
    - Will upload and maintain documentation in project SharePoint/Teams
  - Nathan (Specialty Printers)
    - Gather and share make/model and delivery status for anatomic pathology cassette printers and slide printers
    - Liaise with <PERSON> for procurement and installation details
  - Analysts (<PERSON>, <PERSON>, other APEX team members)
    - Perform LWS mapping once stub records are available
    - Complete printer classification and LOM mapping as directed by <PERSON>m
    - Help define on-site scheduling and availability
- Current Build Status
  - Complete: EPR and LWS record build for workstations and standard printers
  - Pending:
    - Epson label printers: special driver configuration (1 hour per queue)
    - Pharmacy receipt printers: special settings (quick build)
    - EPS print queues: must wait for physical network connectivity (DNS/IP) before building
    - Slide and cassette printers: awaiting detailed specs and installation status
- Naming Conventions
  - APL/AWL: Anatomic pathology printers (AWL if wireless; APL if Ethernet)
  - CPL/CWL: Clinical pathology printers (similarly CWL if wireless)
  - Separate ranges reserved for Zebra and slide-printer classes
- Process and Coordination
  - Incremental Deployment:
    - Printers installed online in rolling phases
    - Charles to be notified as each printer comes online for EPS queue build
    - Analysts to perform print mapping (which can be done in advance) and TDR testing after build
  - Communication Pathways:
    - Printer provisioning notifications → Charles’s team
    - Stub-record availability → Analysts (Shim, Katie, Courtney)
    - Document updates & master list storage → Shared project SharePoint / Teams channel
- Timeline & Milestones
  - 6/18: Current date; kick-off meeting held
  - 6/25: Complete investigation of printing requirements (OTX, unit-based mapping)
  - 6/30: Finish build of all EPR and LWS records and any preliminary EPS queues where possible
  - Week of 7/7 (Pilot TDR)
    - Conduct pilot in 8 East and 8 West
    - Verify end-to-end print mapping and TDR functionality
  - 7/21: Target date to complete all print mappings ahead of full training
  - Week of 8/4 (Official TDR Start)
    - Begin full TDR process with training, pivot-point setup, and go-live readiness
- Documentation & Tracker
  - Use the master list (East/West, device alignment) provided by Jeff/Corey
  - Store in St. Francis/St. Mary’s project SharePoint, under “Hardware and Devices”
  - Share unaltered copy in meeting chat; maintain single source of truth
  - Project manager to set up a rolling tracker (weekly or as-needed cadence)
- Action Items
  - Charles
    - Finalize Epson and receipt-printer EPR/LWS builds today
    - Build EPS queues upon notification of network availability
  - Nathan
    - Retrieve and share make/model/specs for slide and cassette printers
    - Coordinate with Stephen Chu on procurement/installation
  - Shim
    - Send list of system-level VLP-mapped classes to Jen for note capturing
    - Work with Tanya and David to outline OTX and Optime print requirements
  - Jeff/Corey
    - Upload master printer/workstation list to SharePoint
    - Provide access and link to Integrated IT Teams site
  - Analysts (Katie, Courtney)
    - Schedule on-site availability and confirm resource allocation
    - Prepare for LWS and LOM mapping once stub records are ready
  - Project Manager
    - Establish meeting cadence and maintain rolling tracker
    - Confirm deadlines and monitor progress against milestones