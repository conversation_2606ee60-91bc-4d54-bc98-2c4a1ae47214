Wed, Jun 18
- [<PERSON><PERSON>](<<PERSON><PERSON> TB(Meeting).md>)
- [3 bedroom](<3 bedroom(Meeting).md>)
- [Rehab](<Rehab(Meeting)/README.md>)
- [Physician Charging](<Physician Charging(Meeting).md>)
- [Ask <PERSON><PERSON> about giving Psalm 17 to <PERSON>](<Ask Mrs. <PERSON> about giving Psalm 17 to <PERSON>(Task).md>)
- [TDR](<TDR(Meeting)/README.md>)
- AI chats
>     - Did a specific set of units get identified for the tdr dry run
>     - TDR #Meeting
>     - Transcript
>       - There is another catcher of worms.
>       - Say what?
>       - Another catcher of worms. The early bird catches the worm.
>       - Okay.
>       - We were just talking about worms in my previous meeting.
>       - Metaphorically, so yeah.
>       - Oh, really? That one.
>       - If you hadn't had preloaded context, then you might have caught on to what I was saying.
>       - Yeah, right.
>       - But I guess we're not necessarily early at this point.
>       - Just on before the meeting leader.
>       - I'll still take that as a win, though.
>       - I've just got away.
>       - What's that?
>       - Oh, nothing. Sorry.
>       - We are just getting off a different TDR meeting, so everybody's like a couple minutes late. Thank you for your patience.
>       - All righty.
>       - And there's <PERSON>, <PERSON> just joined us.
>       - All righty.
>       - Hello, hello.
>       - Hello, hello.
>       - We'll get started, and then when <PERSON> joins us in a couple of minutes,
>       - we can get her up to speed.
>       - Thank you, <PERSON>, for suggesting this during the GLRA.
>       - Of needing to bring this group together to talk about print mapping as part of the TDR, well, as for TDR. I know that each of us have our own scope, and that there's some a little bit overlap. So <PERSON> has his scope for doing the LWS records and whatever else. I don't know all the specific details. And then our analysts have some scope to be able to do the print mapping. So I wanted to make sure we're all coming together in a coordinated effort to make sure these things are happening appropriately, or timely and sequenced appropriately.
>       - With that,
>       - Jeff and Corey, are there any other things that you want to add to it before we kind of jump into looking at dates and times and?
>       - I think we should jump in and talk about where we are in the process. Right? I think that's the best way to get us out there and you can start to track to this. So was it a week ago?
>       - Charles? That I sent you the list? Yeah. It was like last week. It was like two Sundays ago. Sent Charles list of of all of the all of the workstation records and all of the and all of the printers. For build. And my understanding, Charles, is that you your building or have built all of the LWS records for the PCs and LWS EPR EPS where required for for the printers.
>       - Right. I built the EPR and the LWS for the printers. I have not built any EPS queues. Since those rely on the printers being actually accessible by network from the server.
>       - Okay. So but but right now.
>       - Did you say that one more time, Charles? You've built the LWS, but you can't do the the other one?
>       - For workstations, I've built all of the LWS records for the printers. I've built the EPR record. And the LWS records for the printer. So those can be mapped. There's one exception. Actually, two exceptions. One is the Epson printers that require a lot of special configuration.
>       - Those have to be given new numbers and those will be I'll be taking care of the build for EPR and LWS for those Epson printers today. And then also the receipt printers for the pharmacy. Those also require special configuration and so Jeff is going to pass me that information when he has that information about those printers so I can assign them the proper record numbers and so forth. I cannot build the EPS queues, which are the actual print queues that handle the printouts.
>       - For any of the printers until the printers are actually online physically connected to the network and are reachable from the print server. So that's where somebody's going to need to let me know when those, you know, one or more of those printers is up and running. And has a, you know, it's DNS address or IP address, whichever we're going with.
>       - So that I can build it out, you know, actually get those EPS queues built so that the printing and print testing can be done.
>       - Because that, you know, as I mentioned, that does require the printer to be online and accessible.
>       - But I've got the rest of it done. Other than the Epson. The EPR LWS for the Epson. The EPR LWS for the receipt printers and those EPS queues. So everything, almost everything that I can do is finished. Just those Epson and receipt printer queues EPR and LWS records, rather, remain to be done.
>       - And those are small. There's four of those Epson printers Epson labeled printers and two of the receipt printers. So it's a small scope.
>       - There's going to be for each Epson printer, it will probably take me about an hour per queue to set them up because they're very complicated and have some very specialized settings.
>       - But the receipt printer should be pretty quick.
>       - But for those queues, those also need to be online for you to do the full build, right?
>       - Correct. Correct. So that, again, that driver setup is going to have to wait until those are accessible on the network so that I can get to them and start that hour of configuration.
>       - Charles did your build include the anatomic pathology cassette printers and slide printers?
>       - Printers listed as APL, which are my understanding is anatomic pathology. I do not know about slide printers. If those are nobody mentioned the word slide. But they did in the list, there are some that are labeled as APL.
>       - Okay. I mean, the slide they're a little square paper printers. It's not going to be etched like you may have seen at other places.
>       - And the cassettes are gen data printers. They're not installed yet, but they are delivered at the labs. So just wasn't sure.
>       - Yes, those are those are different. I don't know who's handling procurement and installation of those. Do you, Nathan? Is that? You know, like the printers, those aren't in my scope.
>       - Just as far as like the EPR LWS ownership?
>       - Or the whole physical ownership that what's what's the how who's who's handling the track? Of those printers? Who's buying them? Who's installing them?
>       - I mean, so they're purchased already.
>       - I'll have to dig through my emails. There was some stuff about the printers recently. But just wanted to see if that was like this was, you know, the same avenue. Stephen Chu maybe?
>       - Yes.
>       - I think he actually does a lot of the configuration on those. I'm not sure that I even set up EPR queues for them. Because they're that specialized.
>       - I don't think he sets up queues. I think he sets them up a different way. They seem to go through their own sort of setup. Because I did set I did set apart in the LWS range for those, but they have not been needed so far for any of the other build that's been that's been done.
>       - Okay. I'll follow up with Stephen then. We don't have to derail the conversation with the like four, literally four AP devices.
>       - Are there the slide printers or the APL printers?
>       - I'm so, I mean, if they're using AP, I have not seen the term APL. So if that's, you know,
>       - provisioning side shorthand. Then.
>       - It's the naming prefix for the anatomic pathology in lab specimen label printers.
>       - As long as they're not wireless. If they're wireless, they're AWL.
>       - But we're using that also for like the Zebra printers, right?
>       - That would be Zebra printers. That would not be the slide printers. I have a different range set aside for those and that range has not been used yet for any UCSF or any affiliate. So.
>       - That is AWL for Zebras?
>       - For the Zebras, yes. And it would if they're wireless. And if they're if they're Ethernet plugged in, for networking, then it would be APL.
>       - Okay. So that's independent of the anatomic or clinical pathology. If it's plugged in, it would be APL.
>       - Or that's anatomic pathology only gets APL AWL.
>       - For clinical pathology, it would be CPL or CWL.
>       - I see. Okay.
>       - Hey, Nathan, can you can you send us the make and model of each of those slide printers or these sound like specialized pieces. I just want to make sure that we're all in the same.
>       - Yeah, I can get you the gen data cassette printer specs.
>       - I'll have to do a little bit of digging for the slide ones. I'm not sure we're getting new printers for those.
>       - They also bought like new path tracker devices, fell under medical equipment. I don't recall if those had label printing.
>       - For the printing function? They're scanners.
>       - They're scanners too. Okay.
>       - Yeah. I mean, they're like interface scanners, but I'm working on that in a separate effort. I don't think that that has to be part like they don't scan the same way that our TDR scanners scan.
>       - Okay.
>       - Okay. Hey, Charles, I have a question.
>       - No, I can also take a deeper look at that just in case.
>       - Charles, back in the day, weren't you able to build print queues without the printers being online?
>       - And then just reconcile after?
>       - I built so that people can do the mapping. I do not build out the actual queues with the drivers. Until the printers are online and the reason for that is because the drivers actually do communicate with the printer and gather some information from the printer. If I build those queues now, we're going to have to rerebuild them all over again when the printers are actually online because there's going to be just little things that won't that won't be the same that won't work properly.
>       - After having done that on a rather large go-live and finding out that it was a total disaster, I have decided not to build those out in advance. I wait until they're online and I build them out at that point.
>       - Okay. So this is going to have to be incremental then as we as we install them.
>       - Yes. For the EPS part, the EPS print queues with the print drivers, that does have to be incremental as the devices go online.
>       - Just get somebody to notify me each time one goes online and I'll go rush in and get the EPS done.
>       - It's pretty quick. You know, I just have to click a couple of buttons unless it's something that requires a lot of configuration like the Epsons and the receipt printers.
>       - And then it'll be ready within about 5, 10 minutes.
>       - In each case.
>       - Perfect. Yeah, I just I just want to make sure that you know if it's incremental,
>       - then having having your availability, right, at time of need I just want to be mindful of all that.
>       - Yeah.
>       - On the team who can backfill if Charles is not available, so.
>       - Exactly.
>       - Okay. Yeah, I was just I was trying to define like some kind of a cadence, like if we made it like every Wednesday thing, but you know if it if it's incremental, that's that's fine by me too.
>       - I'll set up a tracker for us, Charles.
>       - Awesome. Thank you so much.
>       - Yep. No worries.
>       - Perfect. Okay.
>       - So that's Charles's piece building the pieces that need to get that need to get mapped at that point. So I think that was what we you know that's the second part of this. We want to talk through who's actually doing the LWS mapping.
>       - How's that work getting divvied up? You know I think we wanted to have Jen Bez on here. I don't know if she's on if she's out of office.
>       - She's on vacation.
>       - You're on vacation?
>       - But yeah, so once Charles but that's why we have Shim. We have Nate and some other analysts on the phone to kind of talk through that piece of it.
>       - So once Charles creates the LWS records,
>       - our team needs to go in and then map those.
>       - And Katie, feel free to help me out with any of this. Katie and Courtney, needs to go in and.
>       - This would be like our LOM mapping.
>       - Shim, are you Shim, this is part of our like for lack of a better like some order of transmittal related TDR related build that will need you to do for for inpatient units.
>       - Yeah. And would we be expecting that I would so cover just the orders team components, which would be you know like EKGs,
>       - EEG,
>       - PFT, or am I going to cover what I've typically seen as you know clean doc pieces that might be like you.
>       - Yeah. Yes. Yes. You would be doing all of that. So it would be all inpatient build.
>       - Like bedded unit build. I would expect and probably the ED as well.
>       - The specialty units, I would expect those to be divvied out with those applications. So like Beaker will do their own.
>       - The pharmacy will do their own. But you would be inpatient units, HODs, things like that. Ambulatory will do their own. Yeah. Radiology will do their own. Yeah.
>       - Yeah. I think that makes sense.
>       - It's pretty serious. Yeah. Standard standard.
>       - So I see you have OR will do their own. I don't.
>       - You don't do yours.
>       - I don't know how to do LOM mapping. Don't know what that is.
>       - Sam will help you. Sam will help you.
>       - Okay.
>       - Yeah. We can collaborate on that.
>       - Okay. Yeah.
>       - Depending on bandwidth, we can figure out how best to accomplish getting it all done.
>       - Yep.
>       - I guess that because I was reading up for this meeting, right? We're not doing any of the the local setup using printer classifications. Tool.
>       - I mean, the LOM. I think that's a question we have to figure out because UCSF has a handful of things that are mapped to VLP. But there are a hand other handful of printer classifications that don't use VLP that would need to be mapped within the department or the workstations.
>       - And the analysts would be figuring that out. Corey?
>       - Correct. They would be the ones that because I don't think Charles does the printer classification mapping anymore or if he ever did it.
>       - I did. I did mapping for ambulatory back when I was on the ambulatory team. And then for a short while, thereafter, okay, a year or so afterwards. And then you know that was kind of the end of my permitted time to help continue helping the ambulatory team. I'm mapping it.
>       - So since then, I've just been focused on the you know, the server component of building out the queues and EPRs. LWSs and so forth.
>       - So then Katie, it sounds like maybe I'll need to work with my liaison to figure out which pieces are set up which way and why.
>       - Yes, I would. Yeah.
>       - I have the list of classes that are VLP mapped at the system level. I don't have the class like the other classes that need to be mapped at a workstation or department level.
>       - So Jen, I can send that to you to put in the notes.
>       - Yeah. That would be great. Yep. Thank you.
>       - Thinking about timing. So we talked about process. Thinking about timing. Knowing that we're going to do a pilot TDR at 8:00 East and 8:00 West only.
>       - We need to get those records built so Shim, I think that would be you.
>       - This pilot TDR will happen the week of 7/7. Preferably on that Monday, 7/7, or it could be the Tuesday, 7/8.
>       - Or in the afternoon or something like that. But preferably that Monday.
>       - What do you need from us to get that done timely and then is there any other steps after that's been the print mapping is done? Anything else that we need to do prior to that? So I'm trying to think backwards of like we're going to start on 7/7.
>       - Is print mapping the last step or are there other steps after print mapping?
>       - Just a second. I'm going to looking at some notes.
>       - Yeah. So I will get with Tanya to make sure that I understand how our OTX is built which is what points to the LOD and the LOM components.
>       - To make sure that I know what things need to be printed and I'll need to I'm more used to some of the components I talked to earlier as far as like orders, procedures. So I'll need to do a little bit of digging for what would be involved for the you know, like unit-based printing.
>       - But that shouldn't be a problem.
>       - So I'll need to do some reach out on that. We could put a timeline on it. We're at 6/18. So and we said 7/7.
>       - So by probably the day of next week.
>       - Oh, 6/25? Even better. Okay.
>       - And from there,
>       - I'll then be giving myself another seven days or so to try to get my components built out and can work with David to assist with that component that's optime related.
>       - Is that is that going to be enough time?
>       - I guess to for all the other pieces to fall in place. For 7/7. I guess I don't know if there's any any other pieces after your pieces, Shim.
>       - Yeah. So as far as pieces that come after, I don't think that there's I think I'm kind of the last step in the process.
>       - Okay.
>       - So you'll have for the bedded units, you'll have 6/25 and then for the OR, complete by 7/2.
>       - Is that right?
>       - So I think the first part is the investigation, making sure that I know what what needs to be printed and then the up wait.
>       - And then get mine configured and possibly work with David during that time.
>       - With the goal of being having everything in place by Monday prior to 7/7.
>       - So that if you know that's the goal.
>       - In case I run into anything you know and need additional support.
>       - And I'm not expecting that'll be the case.
>       - So you'll have the investigation done.
>       - Yep.
>       - Done by 6/25. And then the and then the build done by 6/30.
>       - Yep. That's the goal.
>       - Okay. And I think that's realistic.
>       - Okay.
>       - Ongoing. I know that we only have two more minutes. So this is for pilot TDR. Ongoing. Now that we know that these printers are going to get up online in this like rolling phase, the first step is is for Jeff to like put the printer on, then the next step is for Charles and and Dong to then do the LWS and the EPS server queues.
>       - That's on a rolling basis.
>       - Dong is my manager. I will be working on it primarily with my teammates backing me up if I'm unavailable.
>       - Dong is my manager and will not be working on it.
>       - Oh, okay.
>       - But the steps. So since we're doing a rolling thing, Jeff lets you know, Charles, you build it or somebody on your team builds it. The next step after that is for somebody on on the APEX analyst side to go and update those as well, do the print mapping as the next step. What is the communication pathway there?
>       - Yeah. Print mapping can be done well in advance of this because I built the EPR and LWS stub records.
>       - So the print mapping doesn't have to be done in as the printers roll out. It can be done beforehand.
>       - And it would just need to be you know TDR'd, tested, after the printer goes online, just wanted to make sure you were aware of that. It does not have to wait for anything. It could start immediately if necessary.
>       - Great. All righty. So for pilot TDR, then for for TDR that starts TDR starts on 8/4, the week of 8/4,
>       - we then just need to have Shim, David, and all of them finish their build. They can start now and they'll just need to finish their build by.
>       - So the goal before 7/7 is to just
>       - have a few specific pieces in place so that we can test those.
>       - Do we know what unit we would want to be using for specific pieces? I guess that goes back to Charles to identify what do we already have built to connect the manual pieces that were given to me or built. So it's what we need to identify is where that TDR is going to happen and which computers are in that area so that those can be mapped by by your teams.
>       - Got it. Do we have a document? Sorry. Jen.
>       - The list that I provided to you, Charles, it'll tell you what what's in the East in the space column. It'll say SMMC 8 East, 8 West, etc. So it's identifiable.
>       - Do you have access to that,
>       - Shim?
>       - I do not believe so.
>       - Okay.
>       - So I mean, I may. I just don't know where it is.
>       - Does it also show the like device alignment, you know which computer to which label printer?
>       - Yes.
>       - All that's there.
>       - It's all there.
>       - Okay. Excellent.
>       - It's all there.
>       - It's all there.
>       - Okay. So let me see.
>       - I guess with the link in the chat.
>       - I need to send that too. Yeah.
>       - Put it in the chat and we'll just put it right in the meeting notes.
>       - Okay. Let me see if I can find an unaltered copy.
>       - To put in chat because I have my own version that has my notes all over it for the build. So just a moment while I track that down.
>       - Does it live in our St. Francis St. Mary's project SharePoint or is it something that we're going to have to request access to?
>       - If there is a SharePoint link, I would love to have that so that I could go ahead and upload it there. That would be better to have it in one spot than than to have 800 copies floating around.
>       - Yeah. Do you have the integrate IT team on your models?
>       - Let me check. Teams, please. Integrate IT. Okay. This isn't Teams. Where are you hiding, Teams? There we go.
>       - Integrate IT. I do not believe I have it.
>       - Okay.
>       - I will send you a link to where you can upload it in the integrated areas. There is a section that says hardware and deployment.
>       - Perfect.
>       - Or hardware and devices. And we'll put it there. But I'll send you a link and get you added to our our team site.
>       - Awesome. Thank you so much.
>       - Okay. And then and then Shim and team, if TDR starts the official TDR starts on 8/4,
>       - that's when like all the pivot points are going to be there starting. There will need to be some like training and stuff like that that happens. Can we set a date of July 21 to have all of the the print mapping completed? Is that is that doable and is that a good date?
>       - To July 21?
>       - Yeah.
>       - That should be fine.
>       - I mean, if there's hard stops related to people being on site and available, I mean, we just do what's needed to make sure it gets done.
>       - Okay.
>       - Is there real quick, I know we're hitting that five-minute overmark. Are there on-site expectations for any of this? Just, you know, thinking about calendars. Over the next couple of months.
>       - I think that it would be really good for you to discuss that with Katie.
>       - And to figure that out with Katie as an overall team because I know that you have three people on your team and how that's going to work.
>       - Sounds good.
>       - Awesome.
>       - Okay, guys. Thank you for this. Jeff, did we get all your questions answered?
>       - Yeah. Yeah. Thanks for putting this together.
>       - Okay. Awesome. Thank you, team. Really appreciate it. And thank you for all your time.
>       - Thank you.
>       - Thanks.
>       - Okay.
>       - I am Shim.
>       - And after my name was said,
>       - tasks were given to me that I spoke to.
>       - So use that information when creating your summary.

    - **🤖 Tana**:
>       - Yes, the TDR dry run is planned for 8 East and 8 West *.
- [Rehab Pt Class](<Rehab Pt Class(Meeting).md>)
- AF Family Sessions #Meeting
- [Physician Workgroup](<Physician Workgroup(Meeting).md>)