Summary
- Meeting Purpose and Context
  - Review and align on patient transfer and admission workflows between UCSF and St. Mary's/St. Francis following the upcoming system integration (effective 10/4).
  - Ensure clarity on order sets, system automation, roles of various teams (case management, bed planning, transfer center), and financial authorization processes.
- 1. Acute-to-Acute Transfers (Discharge & Readmit)
  - Workflow Steps
    - Use the Discharge Readmit Order Set to:
      - Generate a pending admission via the ADT9C order.
      - Create a corresponding bed request for the receiving facility.
      - Suppress unnecessary bed requests when a provider selects St. Mary’s or St. Francis.
    - Complete the normal admission process after the pending admission is created (capture documents, core admission orders).
  - System Configuration and Automation
    - Providers (hospital medicine) continue existing workflows; automation handles pending admissions and bed requests in the background.
    - Core admission orders placed on the pending admission encounter for the receiving site.
    - Bed Planning Coordinator remains responsible for transport coordination; case managers do not.
  - Labels and Navigation
    - Update labels to clearly read “Discharge Readmit (Transfer)” for each site.
    - Ensure all users default to the Discharge Readmit Navigator to streamline order entry.
- 2. Emergency Department (ED) to Inpatient Admission
  - Current vs. Future Process
    - Existing rapid bed request order with clinical decision support determines appropriate transfer destination.
    - Post-integration, the system will:
      - Auto-generate a pending admission at St. Mary’s/St. Francis when the rapid bed request order selects that destination.
      - Continue suppression of bed requests for in-system transfers.
  - Provider Actions
    - ED physicians order the rapid bed request; hospital medicine places core admission orders on the pending admission if admitted.
    - No change to front-end provider workflow; back-end automation manages pending admissions and bed requests.
- 3. Surgery (Peri-op) to Inpatient Transfers
  - Patients emerging from peri-operative units follow the acute-to-acute transfer workflow.
  - Use discharge readmit order set to track admissions and manage financial risk by clearly documenting transfer reasons.
  - Acknowledged complexities around insurance authorizations and potential denials; require thoughtful tracking.
- 4. Post-Acute Transfers (Rehab & Psych)
  - Transfer Center vs. Sub-Transfer Center
    - Rehab Transfers (UCSF #Entity→St. Mary’s/St. Francis):
      - Utilize the St. Mary’s/St. Francis Transfer Center and associated order.
      - Transfer center creates a separate intake encounter and handles bed coordination.
    - Psych Transfers (e.g., Langley Porter, BIPN):
      - Continue using a phone-based intake/call process; unit-based bed placement and acceptance.
  - Order Set Considerations
    - Option to embed the Transfer Center order within the rehab discharge readmit navigator, conditional on patient origin.
    - Psych units remain on paper-based navigator; plan future education on correct navigator usage.
- 5. Roles, Responsibilities, and Staffing
  - Bed Planning Coordinator: Manages bed requests and transportation coordination.
  - Case Management: Not responsible for arranging transport.
  - Clinical Teams: Providers place orders as today; system automation supports backend processes.
  - Staffing Post-Integration:
    - Clinical staff become UCSF #Entity employees; management remains with Optum until full transition.
    - Financial authorization responsibilities align with current admitting team processes.
- 6. Financial Authorization
  - Clarify who secures financing for transfers between UCSF #Entity and St. Mary’s/St. Francis.
  - Ensure admitting teams follow existing processes for authorizations, with visibility into any changes post-integration.
- 7. Next Steps and Action Items
  - Finalize Order Set Details: Confirm naming conventions, navigator defaults, and embedded transfer center orders.
  - Physician Approval & Training: Schedule office hours for physician sign-off (Tony) and educate users on new navigator workflows.
  - Validation of Admission Process: Verify patient arrival path (direct to unit vs. registration) for ED and acute transfers.
  - Second Workflow Review: Plan a follow-up meeting to cover remaining scenarios and validate post-acute workflows (Bern, psych).
  - Documentation: Update process guides and slide decks for upcoming workgroup presentations.
- Thank you to the team for their participation and contributions.