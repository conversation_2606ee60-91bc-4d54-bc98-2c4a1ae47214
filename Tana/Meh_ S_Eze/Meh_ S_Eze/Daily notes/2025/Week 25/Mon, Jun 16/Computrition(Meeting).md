Computrition #Meeting
- **Date**: *2025-06-16T16:45:00[America/Chicago]/2025-06-16T17:45:00[America/Chicago]*
- **Action items**:
  - Send the MRN to <PERSON> to help with the burn patient *
  - Begin testing burn patient scenario using <PERSON> in SFH computation *
  - Ask <PERSON> to follow up with <PERSON> and <PERSON> about the workflow *
  - Update adult tube feed TFO21 screen (create category and rename products) *
  - Email <PERSON> to request an updated allergen export *
  - Put the St. Mary’s Luke patient on a Leave of Absence (LOA) in Computrition *
  - Finish testing diet orders and test tube feed O21 scenarios *
  - Send the product list for new tube feed handshakes to <PERSON> for table update *
- Summary
  - Transfer Workflows Testing
    - Inpatient to Pre-op to OR Transfers
      - Verified patient (Luke) transfer from ICU (room 214) to pre-op and then to OR
      - Confirmed diet orders and bed holds remain intact through transfers
      - Validated computations for SMH, SFH, and SFSH schemas
      - Tested typical IR and burn unit transfers—diet orders remain in Computrition without disruptions
    - Burn Unit OR Location Handling
      - Identified burn OR (OR 10) as a location within SFH peri-op rather than a separate department transfer
      - Confirmed that burn patients should remain in peri-op in Computrition, holding original bed assignment
      - Recommended unit manager to move patient to burn OR bed in Epic (visual cue only, no real transfer)
  - Leave of Absence (LOA) Workflow
    - Placed patient (Luke) on LOA and confirmed:
      - Patient remains in original room
      - Diet order and allergies remain intact
    - Returned patient from LOA and validated computations
    - Discussed kitchen communication: tray passers confirm patient presence and status before meal delivery
  - Tube Feeding Order Configuration
    - Initial Tube Feed Testing
      - Placed continuous tube feed orders (TFO21) on test patients
      - Confirmed orders come through Computrition correctly
      - Discussed difference between open systems (UCSF #Entity sites) and closed systems (SMH/SFH) impacting product sizes and mapping
    - Dual‐Product and Category Mapping
      - Identified need for distinct handshake/category names to differentiate 250 mL vs 1 L products
      - Proposed creating new category list or adjusting handshake names (prefix with “S” for SFSM closed system)
      - Tanya to mock up simplified TFO21 screen with sample products (Vivinex, Nutrient 1.5) for testing
      - Matthew to update integration engine table with new handshake names for proper parsing
  - Allergy Table and Special Diet Maintenance
    - Reviewed current allergen mapping process:
      - Monthly updates from Willow/Medispan
      - Orders team (Bruce Pierre, Jennifer Hasel) creates new mappings in Epic
      - Computrition team builds handshake names and tests ingestion
    - Planned future export of full allergen list to ensure SFSM alignment
    - Intention to maintain unified allergen table across all sites
  - Action Items & Next Steps
    - Tanya & Shem:
      - Update adult tube feed questions (add new handshake names, category list)
      - Mock up sample TFO21 screen for closed‐system products
    - Matthew:
      - Load new handshake names into integration table
      - Verify dual‐product and closed‐system parsing works
    - Judy:
      - Complete test transfers from OR back to inpatient units
    - Leila:
      - Continue diet order testing (special food requests, tube feed scenarios, bolus/intermittent, MEND/volume mixing)
      - Prepare questions for nutrition workgroup on allergens and computation
    - Follow-up Meeting:
      - Reconvene once TFO21 updates are built and tested
      - Discuss allergen export results with Bruce and orders team
      - Review tube feeding workflow at nutrition workgroup session