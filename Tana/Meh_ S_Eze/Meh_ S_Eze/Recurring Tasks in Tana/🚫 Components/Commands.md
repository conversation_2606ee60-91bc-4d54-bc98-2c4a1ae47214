Commands
- Mark Tasks Done or Undone
  - ✅ _(Marks task as Done)_
    - [Set done status](https://app.tana.inc/?nodeid=SYS_M024)
    - [Set field values](https://app.tana.inc/?nodeid=SYS_M002)
  - 🚫 _(Marks Done task as Not Done)_
    - [Set done status](https://app.tana.inc/?nodeid=SYS_M024)
    - [Set field values](https://app.tana.inc/?nodeid=SYS_M002)
- Reschedule Tasks
  - Reschedule for the Next Day _(Reschedule for 1 day later than currently scheduled)_
    - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
    - Remove reference from Agenda (if it's a reference)
      - [Run a command line command](https://app.tana.inc/?nodeid=SYS_M019)
  - Reschedule within a few days
    - in 2 days _(Reschedule for 2 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
    - in 3 days _(Reschedule for 3 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
    - in 4 days _(Reschedule for 4 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
    - in 5 days _(Reschedule for 5 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
    - in 6 days _(Reschedule for 6 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
    - in 7 days _(Reschedule for 7 days later than currently scheduled)_
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Remove reference from Agenda (if it's a reference)
  - Reschedule for further out
    - Weeks
      - in 1 week _(Reschedule for 1 week later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 2 weeks _(Reschedule for 2 weeks later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 3 weeks _(Reschedule for 3 weeks later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 4 weeks _(Reschedule for 4 weeks later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
    - Months
      - in 1 month _(Reschedule for 1 month later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 2 months _(Reschedule for 2 months later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 3 months _(Reschedule for 3 months later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 6 months _(Reschedule for 6 months later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
      - in 12 months _(Reschedule for 12 months later than currently scheduled)_
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
        - Remove reference from Agenda (if it's a reference)
- Additional Task Commands
  - Remove as task _(Untag as task. This retains the node and its information but removes it from task searches and your agenda)_
    - Remove task supertag
      - [Remove tags](https://app.tana.inc/?nodeid=SYS_M006)
    - Remove recurring task supertag
      - [Remove tags](https://app.tana.inc/?nodeid=SYS_M006)
    - Remove reference from Agenda (if it's a reference)
  - Shift Scheduled on to Today _(Change the date the task is scheduled for completion to Today. Useful if you're looking ahead a few days and decide to do a task now instead of later, or to quickly change an overdue task to be due today.)_
    - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
- Specific to Recurring tasks
  - 🚀 Initialize Recurring Task _(Initializes new recurring task. Schedule the first instance of this task, set its recurrence parameters, then click this button. You'll only use this once for each recurring tasks)_
    - Set Originally Scheduled on date
      - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
  - 🚀 _(Same as "Initialize Recurring Task")_
    - Set Originally Scheduled on date
  - ✅ ➡️  _(Leaves a completed task on the record and sets next recurrence)_
    - Set recurring task to done to prepare to duplicate it
      - [Set done status](https://app.tana.inc/?nodeid=SYS_M024)
      - [Set field values](https://app.tana.inc/?nodeid=SYS_M002)
    - Duplicate the recurring task to leave a record of the completed work
      - [Run a command line command](https://app.tana.inc/?nodeid=SYS_M019)
    - Reset recurring task to NOT done to schedule next occurrence
      - [Set done status](https://app.tana.inc/?nodeid=SYS_M024)
      - [Set field values](https://app.tana.inc/?nodeid=SYS_M002)
    - Schedule next occurrence
      - Set next occurrence (completed)
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Set next occurrence (scheduled)
        - [Insert relative date](https://app.tana.inc/?nodeid=SYS_M026)
      - Set Originally Scheduled on date
    - Remove reference from Agenda (if it's a reference)
  - Skip Instance _(Pushes to next occurrence without completing | recurring tasks only)_
    - Schedule next occurrence
    - Remove reference from Agenda (if it's a reference)
- Setup Command _(Only use if your #task supertag uses the built-in "Due Date" field)_
  - Convert Due Date to Scheduled on
    - [Insert cloned copies of nodes](https://app.tana.inc/?nodeid=SYS_M010)
    - [Remove fields](https://app.tana.inc/?nodeid=SYS_M022)
- On-added Commands
  - Set to today if not already scheduled
    - Shift Scheduled on to Today _(Change the date the task is scheduled for completion to Today. Useful if you're looking ahead a few days and decide to do a task now instead of later, or to quickly change an overdue task to be due today.)_
- Background Commands
  - Remove task supertag
  - Remove recurring task supertag
  - Set recurring task to done to prepare to duplicate it
  - Duplicate the recurring task to leave a record of the completed work
  - Reset recurring task to NOT done to schedule next occurrence
  - Schedule next occurrence
  - Remove reference from Agenda (if it's a reference)
  - Set next occurrence (completed)
  - Set next occurrence (scheduled)
  - Set Originally Scheduled on date