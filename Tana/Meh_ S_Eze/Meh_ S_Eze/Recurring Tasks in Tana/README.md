Recurring Tasks in Tana
- 👉 Start here
  - Overview
    - Welcome! I'm <PERSON><PERSON><PERSON><PERSON>, Productivity Consultant and <PERSON><PERSON> Ambassador.
    - I created the free Recurring Tasks in Tana template so you can have fully-functional, easy-to-use recurring tasks in your Tana workspace!
    - Since you have the template installed, we can assume you have completed the installation video. Before using the template, [continue watching the videos](https://community.rjnestor.com/c/rtt-template-tips/sections/533705/lessons/2003598) to learn how to use recurring tasks!
    - NOTE: You won't ever need to click on the 🚫 Components tab. Feel free to have a look at how the template works, but be careful not to change the core functionality.
  - Learn about the full Tana for Action template
    - This Recurring Tasks in Tana template is excerpted and adapted from the paid course and template, Tana for Action. Tana for Action contains a full Tana implementation of the [Action-Powered Productivity](https://actionpoweredproductivity.com) approach to task, project, and life management.
    - Learn more about Tana for Action here: [rjnestor.com/tana-for-action](https://rjnestor.com/tana-for-action)
  - Template info
    - Creator: <PERSON><PERSON><PERSON><PERSON>
      - ![<PERSON><PERSON> Ambassador](https://firebasestorage.googleapis.com/v0/b/tagr-prod.appspot.com/o/notespace%2Frjnestor.businesscoach%40gmail.com%2Fuploads%2F2023-03-24T15%3A37%3A06.064Z-Tana%20Ambassador%20card.png?alt=media&token=df5a19a9-2c7b-4ec3-be45-51967a5920a5)
      - [rjnestor.com](https://rjnestor.com)
      - [R.J. Nestor](https://www.youtube.com/c/RJNestorCoach) on YouTube
      - [@rjnestor](https://twitter.com/rjnestor) on Twitter/X
      - [rjnestor](https://www.linkedin.com/in/rjnestor/) on LinkedIn
      - [rjnestor.com](https://bsky.app/profile/rjnestor.com) on BlueSky
      - @R.J. Nestor on Tana Slack community
    - Support
      - If you have any questions about the template, or you'd like to learn more about Action-Powered Productivity, DM me in the community!
    - Disclaimer
      - Tana encourages you to build your own tools, as well as adopt and adapt workflows from others. I encourage that too! That said...
      - I can't promise everything will work if you make changes to the core functionality.
      - When you build on top of this Recurring Tasks in Tana template—say, by extending rather than altering supertags—everything should work fine. If you have challenges, DM me in the community!
- [🚫 Components](%F0%9F%9A%AB%20Components/README.md)