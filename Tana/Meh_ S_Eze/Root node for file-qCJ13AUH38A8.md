Root node for file:qCJ13AUH38A8
- [Library](Library/README.md)
- [Inbox](Inbox.md)
- Searches
- Schema
  - #Day
  - #Week
  - #Person
  - #Person - Known
  - #Person - Referenced
  - #Entity
  - #To discuss
  - #Task
  - #Meeting
  - #Article
  - #Memo
  - #Idea
  - #Question
  - #Brainstorm
  - #Daily prep
  - #Weekly reflection
  - #H2 - Area
  - **Context**
  - #Relationship
  - #30s
  - #Tags
  - #Nursing Assesmnet
  - **#task
AND (PARENT: @Tag**
  - #Integrated Testing
  - #H2S - Area
  - **Entity**
  - **Area**
  - #Project
  - **Project**
  - **H2 Area**
  - #H5 Purpose & Principles
  - #H5S Purpose & Principles
  - #H4 - Vision
  - #H3 - Goals & Objectives
- [Meh_ S_Eze](<Meh_ S_Eze/README.md>)
- Pins _(Pinned nodes for ease of access)_
  - Pinned
    - Welcome to Tana! _(Expand the content below for a brief intro)_
    - #Task
    - #Meeting
    - #Article
    - #Memo
    - #Brainstorm
    - #Daily prep
    - #Weekly reflection
- [Deleted Nodes](<Deleted Nodes/README.md>)
- [List of layouts for qCJ13AUH38A8](<List of layouts for qCJ13AUH38A8.md>)
- MoveTo list _(Nodes on this list are shown in the MoveTo menu)_
- Private drafts for chat posts qCJ13AUH38A8 _(Temporary storage of draft replies to chat nodes qCJ13AUH38A8)_
- List of sidebar areas qCJ13AUH38A8 _(Areas in sidebar qCJ13AUH38A8)_
  - Meh_ S_Eze
  - [Shared](https://app.tana.inc/?nodeid=7VTcVBrJk2TC)
  - [Meh-S-Eze (old)](https://app.tana.inc/?nodeid=9VQxSvpLnoql)
  - [Demo](https://app.tana.inc/?nodeid=z3qQEqfP02ro)
- Quick add for qCJ13AUH38A8 _(Quick add notes qCJ13AUH38A8)_
- Avatar
- Users
  - mehseze@gmail_com