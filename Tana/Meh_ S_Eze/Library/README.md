Library
- Welcome to Tan<PERSON>! _(Expand the content below for a brief intro)_
  - What is <PERSON><PERSON>?
    - It's a combination of advanced, connected note-taking and an all-in-one workspace with powerful AI and voice-based features.  
    - Perhaps the most unique thing about <PERSON><PERSON> is the Supertag. Supertags let you define what thing a note is—e.g. is it a #Person, a #Task, an #Article, or a #Meeting. The note will get that structure, be sent to that workflow, and show up anywhere you want that type of thing to appear. Combined with AI and voice recordings it's magic. Learn more at [Supertags](https://tana.inc/supertags)
  - What can you use Tana for? _(Common use cases)_
    - All types of notes and voice memos
    - As a personal/professional knowledge base or wiki 
    - Task & project management 
    - To quickly capture ideas and thoughts on the go
    - To get meeting transcripts, summaries, and the action items sent to your task feeds
    - To replace loads of standalone apps like...  _(Expand to view examples ↓)_
      - Hiring apps 
      - CRMs 
      - Content pipelines
      - UX research apps (or any type of research app)
      - Bug and issue tracking 
      - Custom voice to text workflows 
    - <PERSON><PERSON> is ridiculously versatile. With Supertags #Question you can replicate most productivity apps. Your imagination is basically the limit. 
    - For more inspiration we suggest [x] joining the Tana community #Task and checking out what others are building. 
  - How to use Tan<PERSON> on desktop _(Tan<PERSON> on desktop is a powerful all-in-one workspace)_
    - <- bullets with halos have content nested underneath _(Expand by clicking the arrow (>) or ⌘ + ↓ / ctrl + ↓)_
      - You can collapse it with  ⌘ + ↑ / ctrl + ↑
    - You can click a bullet to get a page view  _(Click on the bullet or ⌘ + . / alt + ->)_
      - And zoom out with ⌘ + , / alt + <-
    - Nest content by indenting and outdenting _(Tab to indent and ⇧ + tab to outdent)_
      - This is how you create a hierarchy in Tana, kind of like folders and subfolders. 
    - Where do I take notes? _(This is completely up to you, but we recommend the following ↓)_
      - Take most notes on your Today page. This is your daily scratchpad, and you get a fresh one each day.  
      - If you want to create a clear hierarchy that you'll revisit over time you can create a wiki structure under your Workspace (your name in the sidebar)
      - For specific things where you already have a Supertag, like #Task, #Idea, #Entity etc. you can go to that thing's Supertag page and add your notes directly there.
    - Popular Shortcuts _(You can view more shortcuts in the bottom of your left sidebar)_
      - Right-click a note to see tools and actions
      - To bring up all actions, type ⌘ + k / ctrl + k
      - To link to other content, type @
      - Type ⌘ + ⏎ / ctrl ⏎ to add a checkbox
      - To do a global search, type cmd + s
      - To search and open a new tab, type cmd + t
      - Hold ⌘ / ctrl and click to open in a new tab
      - Hold shift and click to open in a new panel
  - How to use Tana on mobile _(A lightweight companion in your pocket)_
    - If you don't have it you can get the mobile app here [Tana mobile](https://tana.inc/tana-mobile)
    - Use the app to quickly capture ideas and thoughts on the go, and have easy access to your Tana content.  _(Expand for a few starter tips)_
      - Add the lock screen widget to capture things super fast -> How do I add Tana's lock screen widgets on iPhone? #Question
      - Turn fuzzy thoughts into useful things with voice memos 
        - To do this select a Supertag when capturing a voice memo. This will instantly turn the voice capture into that thing (like a #Task, an #Idea, a #Weekly reflection etc).
    - There are limits to configuring and setting up new Supertags and workflows on the mobile app. We recommend you do this on desktop.
  - If you get stuck _(Tana docs and community resources)_
    - Check the help section in the bottom left sidebar _(Click the (?) icon and you'll get tutorials, docs, and guides)_
    - Join [tana.inc/community](https://tana.inc/community) to get assistance from 20,000+ community members across Slack, Reddit, or YouTube
    - Check out [tana.inc/learn](https://tana.inc/learn) to explore documentation and blog posts from the Tana team
- [Google Calendar Events](<Google Calendar Events/README.md>)
- [Google Calendar Contacts](<Google Calendar Contacts/README.md>)
- meeting
- Cut-Over
- Order
- CORe