# Tana Supertag System Analysis

## Overview
This document provides a comprehensive visual analysis of the supertag and field configuration system found in the Tana JSON export file `Mehs.json`. The analysis is based on the Tana import tools format specification from https://github.com/tanainc/tana-import-tools.

## File Statistics
- **Total documents**: 77,569
- **Supertags (tagDef)**: 49
- **Field definitions (attrDef)**: 62
- **Tuples**: 28,764
- **Meta nodes**: 10,031

## System Architecture

### Core Components
The Tana supertag system is built on three fundamental components:

1. **supertag (SYS_T01)**: The core supertag definition that defines what supertags are
2. **field-definition (SYS_T02)**: Defines all field definitions and their properties
3. **meta information (SYS_T16)**: Contains metadata about nodes

### Base Types
Tana provides 13 built-in base types that serve as templates for common entities:
- **meeting (SYS_T98)**: Meetings, appointments, etc.
- **person (SYS_T99)**: Persons, attendees, candidates, etc.
- **task (SYS_T100)**: Todo, tasks, work items, etc.
- **organization (SYS_T101)**: Company, organization, etc.
- **location (SYS_T102)**: Place, location, etc.
- **event (SYS_T103)**: Event, happening, etc.
- **project (SYS_T104)**: Project, etc.
- **topic (SYS_T105)**: Topic, etc.
- **article (SYS_T117)**: Article, etc.
- **memo (SYS_T118)**: Memo, etc.
- **reflection (SYS_T119)**: Reflection, etc.
- **day (SYS_T124)**: Day node
- **week (SYS_T125)**: Week node

## Field Configuration System

### Field Properties
Each field definition can have the following properties:

1. **Nullable (SYS_A01)**: Whether the field can be left empty
2. **Cardinality (SYS_A10)**: Single value vs. multiple values
3. **Type Choice (SYS_A02)**: Data type selection
4. **Autocollecting (SYS_T44)**: Auto-collect new options
5. **Hide field (SYS_T60)**: Visibility conditions
6. **Semantic function (SYS_T82)**: Field's semantic purpose
7. **Formula (SYS_T28)**: Computed values
8. **Shared Field (SYS_T30)**: Reusable across workspace
9. **Initialization (SYS_T40)**: Default values

### Data Types
Tana supports 10 different data types:
- **Plain Text (SYS_D06)**: Basic text content
- **Checkbox (SYS_D01)**: Boolean true/false
- **Integer (SYS_D02)**: Whole numbers
- **Number (SYS_D08)**: Decimal numbers
- **Date (SYS_D03)**: Date values
- **Tana User (SYS_D09)**: User references
- **E-Mail (SYS_D11)**: Email addresses
- **URL (SYS_D10)**: Web links
- **Options from supertag (SYS_D05)**: Dynamic options from another supertag
- **Options (SYS_D12)**: Predefined choice lists

### Visibility Options
Fields can be hidden based on various conditions:
- **Never hide (SYS_V54)**: Always visible
- **Hide when empty (SYS_V56)**: Hide if no value
- **Hide when not empty (SYS_V57)**: Hide if has value
- **Hide when default (SYS_V61)**: Hide if default value
- **Always hide (SYS_V52)**: Never visible

## User-Defined Supertags

### Person-Related Tags
- **Person**: Custom person tag with relationship and context fields
- **Person - Known**: For known contacts
- **Person - Referenced**: For referenced people

### Meeting & Event Tags
- **Meeting**: Custom meeting tag with status, date, attendees, location fields

### Task Management Tags
- **Task**: Basic task with status, priority, hours tracking
- **task (RTT)**: Recurring task template
- **recurring task (RTT)**: Advanced recurring tasks

### Content & Knowledge Tags
- **Article**: Article content
- **Memo**: Memo content
- **Brainstorm**: Voice-enabled brainstorming
- **Daily prep**: Voice-enabled daily preparation
- **Weekly reflection**: Voice-enabled weekly reflection
- **To discuss**: Discussion items
- **Idea**: Ideas
- **Question**: Questions

### Organizational Tags
- **Entity**: General entity classification
- **Project**: Project management
- **H2S - Area**: Hierarchical area classification
- **Relationship**: Relationship types

### Time-Based Tags
- **Day**: Daily nodes
- **Week**: Weekly nodes

## Key Insights

1. **Voice Integration**: Several supertags are specifically designed for voice input (Brainstorm, Daily prep, Weekly reflection)

2. **Hierarchical Organization**: The system supports hierarchical classification with H2/H2S area tags

3. **Task Management**: Sophisticated task management with recurring task templates (RTT)

4. **Relationship Modeling**: Strong support for modeling relationships between people, projects, and entities

5. **Flexible Field System**: Highly configurable field system with visibility rules, data types, and semantic functions

6. **Meta-Programming**: The system is self-describing with supertags for defining supertags and fields

## Visual Representations

Three Mermaid diagrams have been created to visualize different aspects of the system:

1. **Tana Supertag System Architecture**: Shows the overall system structure and relationships
2. **Tana Field Configuration System**: Details the field definition system and options
3. **User-Defined Supertags and Their Field Relationships**: Maps specific user supertags to their fields

This analysis reveals a sophisticated knowledge management system with strong support for structured data, relationships, and flexible content organization.
