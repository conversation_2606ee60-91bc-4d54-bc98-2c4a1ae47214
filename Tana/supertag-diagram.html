<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tana Supertag Diagram</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .control-group label {
            font-size: 0.9rem;
            font-weight: 500;
        }

        select, button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }

        select option {
            background: #4a5568;
            color: white;
        }

        button:hover, select:hover {
            background: rgba(255,255,255,0.3);
        }

        .diagram-container {
            width: 100vw;
            height: calc(100vh - 80px);
            position: relative;
            overflow: hidden;
        }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.85rem;
            pointer-events: none;
            z-index: 1000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tooltip.visible {
            opacity: 1;
        }

        .tooltip h4 {
            margin-bottom: 0.5rem;
            color: #ffd700;
        }

        .tooltip .fields {
            margin-top: 0.5rem;
            font-size: 0.8rem;
        }

        .tooltip .field {
            background: rgba(255,255,255,0.2);
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            margin: 0.2rem 0.2rem 0 0;
            display: inline-block;
        }

        .legend {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(255,255,255,0.95);
            padding: 1rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            z-index: 100;
        }

        .legend h3 {
            margin-bottom: 0.75rem;
            font-size: 1rem;
            color: #2d3748;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            font-size: 0.85rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .zoom-controls {
            position: absolute;
            bottom: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: rgba(255,255,255,0.9);
            color: #4a5568;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .zoom-btn:hover {
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #718096;
            font-size: 1.2rem;
        }

        /* Node and link styles will be applied via D3 */
        .node {
            cursor: pointer;
            stroke-width: 2px;
        }

        .node:hover {
            stroke-width: 3px;
        }

        .link {
            stroke: #94a3b8;
            stroke-opacity: 0.6;
            stroke-width: 1px;
        }

        .node-label {
            font-size: 11px;
            font-weight: 500;
            text-anchor: middle;
            pointer-events: none;
            fill: #2d3748;
        }

        .group-label {
            font-size: 14px;
            font-weight: 600;
            text-anchor: middle;
            fill: #4a5568;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Tana Supertag Relationship Diagram</h1>
        <div class="controls">
            <div class="control-group">
                <label>View:</label>
                <select id="view-select">
                    <option value="user">My Supertags</option>
                    <option value="all">All Supertags</option>
                    <option value="system">System Only</option>
                </select>
            </div>
            <div class="control-group">
                <label>Layout:</label>
                <select id="layout-select">
                    <option value="force">Force Layout</option>
                    <option value="radial">Radial Layout</option>
                    <option value="hierarchical">Hierarchical</option>
                </select>
            </div>
            <button id="reset-btn">Reset View</button>
        </div>
    </div>

    <div class="diagram-container">
        <svg id="diagram"></svg>
        <div class="tooltip" id="tooltip"></div>
        <div class="loading" id="loading">Loading your supertag diagram...</div>
        
        <div class="legend">
            <h3>Supertag Types</h3>
            <div class="legend-item">
                <div class="legend-color" style="background: #4facfe;"></div>
                <span>Person</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #fa709a;"></div>
                <span>Meeting</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #a8edea;"></div>
                <span>Task</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #ffecd2;"></div>
                <span>Content</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #667eea;"></div>
                <span>Organization</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #48bb78;"></div>
                <span>Time</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #718096;"></div>
                <span>System</span>
            </div>
        </div>

        <div class="zoom-controls">
            <button class="zoom-btn" id="zoom-in">+</button>
            <button class="zoom-btn" id="zoom-out">−</button>
            <button class="zoom-btn" id="zoom-reset">⌂</button>
        </div>
    </div>

    <script>
        let tanaData = null;
        let allSupertags = [];
        let currentView = 'user';
        let currentLayout = 'force';
        let svg, g, simulation, zoom;

        // Color schemes for different supertag types
        const typeColors = {
            person: '#4facfe',
            meeting: '#fa709a', 
            task: '#a8edea',
            content: '#ffecd2',
            organization: '#667eea',
            time: '#48bb78',
            system: '#718096'
        };

        // Supertag type classification
        const supertagTypes = {
            person: ['person', 'people', 'contact', 'attendee', 'known', 'referenced'],
            meeting: ['meeting', 'event', 'appointment', 'call'],
            task: ['task', 'todo', 'recurring', 'rtt'],
            content: ['article', 'memo', 'brainstorm', 'prep', 'reflection', 'idea', 'question', 'discuss'],
            organization: ['entity', 'project', 'area', 'relationship', 'h2', 'h3', 'h4', 'h5'],
            time: ['day', 'week', 'daily', 'weekly'],
            system: ['meta', 'field', 'definition', 'data', 'value', 'enum']
        };

        // Initialize the diagram
        function initDiagram() {
            const container = document.querySelector('.diagram-container');
            const width = container.clientWidth;
            const height = container.clientHeight;

            svg = d3.select('#diagram')
                .attr('width', width)
                .attr('height', height);

            // Create zoom behavior
            zoom = d3.zoom()
                .scaleExtent([0.1, 4])
                .on('zoom', (event) => {
                    g.attr('transform', event.transform);
                });

            svg.call(zoom);

            // Create main group for zooming/panning
            g = svg.append('g');

            // Setup zoom controls
            setupZoomControls();
        }

        function setupZoomControls() {
            document.getElementById('zoom-in').addEventListener('click', () => {
                svg.transition().call(zoom.scaleBy, 1.5);
            });

            document.getElementById('zoom-out').addEventListener('click', () => {
                svg.transition().call(zoom.scaleBy, 1 / 1.5);
            });

            document.getElementById('zoom-reset').addEventListener('click', () => {
                svg.transition().call(zoom.transform, d3.zoomIdentity);
            });
        }

        // Load and process Tana data
        async function loadTanaData() {
            try {
                const response = await fetch('Mehs.json');
                tanaData = await response.json();
                processSupertags();
                renderDiagram();
                setupEventListeners();
                document.getElementById('loading').style.display = 'none';
            } catch (error) {
                console.error('Error loading Tana data:', error);
                showFileInput();
            }
        }

        function showFileInput() {
            document.getElementById('loading').innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem;">Load Your Tana Configuration</h3>
                    <p style="margin-bottom: 1rem; color: #718096;">
                        Please select your Mehs.json file:
                    </p>
                    <input type="file" id="file-input" accept=".json" style="
                        padding: 0.75rem;
                        border: 2px dashed #cbd5e0;
                        border-radius: 8px;
                        background: #f7fafc;
                        cursor: pointer;
                    ">
                </div>
            `;
            
            document.getElementById('file-input').addEventListener('change', handleFileSelect);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    tanaData = JSON.parse(e.target.result);
                    processSupertags();
                    renderDiagram();
                    setupEventListeners();
                    document.getElementById('loading').style.display = 'none';
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    alert('Error parsing JSON file.');
                }
            };
            reader.readAsText(file);
        }

        function processSupertags() {
            if (!tanaData) return;
            
            const docs = tanaData.docs || [];
            const supertags = docs.filter(doc => doc.props._docType === 'tagDef');
            
            allSupertags = supertags.map(supertag => {
                const name = supertag.props.name || supertag.id;
                const isSystem = supertag.id.startsWith('SYS_');
                const type = classifySupertag(name.toLowerCase(), isSystem);
                
                // Get fields for this supertag
                const fields = getFieldsForSupertag(supertag, docs);
                
                return {
                    id: supertag.id,
                    name: name,
                    description: supertag.props.description || '',
                    type: type,
                    isSystem: isSystem,
                    fields: fields,
                    children: supertag.children || [],
                    size: Math.max(20, Math.min(60, (fields.length * 8) + 20))
                };
            });
        }

        function classifySupertag(name, isSystem) {
            if (isSystem) return 'system';
            
            for (const [type, keywords] of Object.entries(supertagTypes)) {
                if (keywords.some(keyword => name.includes(keyword))) {
                    return type;
                }
            }
            return 'organization';
        }

        function getFieldsForSupertag(supertag, docs) {
            if (!supertag.children) return [];
            
            return supertag.children
                .map(childId => docs.find(doc => doc.id === childId))
                .filter(child => child && (child.props._docType === 'attrDef' || child.props.name))
                .map(field => ({
                    name: field.props.name || field.id,
                    description: field.props.description || ''
                }))
                .slice(0, 15);
        }

        function renderDiagram() {
            // Clear previous diagram
            g.selectAll('*').remove();

            // Filter supertags based on current view
            let filteredSupertags = allSupertags;
            if (currentView === 'user') {
                filteredSupertags = filteredSupertags.filter(st => !st.isSystem);
            } else if (currentView === 'system') {
                filteredSupertags = filteredSupertags.filter(st => st.isSystem);
            }

            // Create links based on relationships
            const links = createLinks(filteredSupertags);

            // Create nodes
            const nodes = filteredSupertags.map(d => ({...d}));

            if (currentLayout === 'force') {
                renderForceLayout(nodes, links);
            } else if (currentLayout === 'radial') {
                renderRadialLayout(nodes, links);
            } else if (currentLayout === 'hierarchical') {
                renderHierarchicalLayout(nodes, links);
            }
        }

        function createLinks(supertags) {
            const links = [];
            const supertagMap = new Map(supertags.map(st => [st.id, st]));

            // Create links based on shared field types and naming patterns
            supertags.forEach(source => {
                supertags.forEach(target => {
                    if (source.id !== target.id) {
                        const similarity = calculateSimilarity(source, target);
                        if (similarity > 0.3) {
                            links.push({
                                source: source.id,
                                target: target.id,
                                strength: similarity
                            });
                        }
                    }
                });
            });

            return links;
        }

        function calculateSimilarity(a, b) {
            // Same type gets higher similarity
            if (a.type === b.type) return 0.8;
            
            // Check for shared field names
            const aFields = new Set(a.fields.map(f => f.name.toLowerCase()));
            const bFields = new Set(b.fields.map(f => f.name.toLowerCase()));
            const intersection = new Set([...aFields].filter(x => bFields.has(x)));
            
            if (intersection.size > 0) {
                return 0.4 + (intersection.size / Math.max(aFields.size, bFields.size)) * 0.4;
            }

            // Check for name similarity
            if (a.name.toLowerCase().includes(b.name.toLowerCase()) || 
                b.name.toLowerCase().includes(a.name.toLowerCase())) {
                return 0.3;
            }

            return 0;
        }

        function renderForceLayout(nodes, links) {
            const width = svg.attr('width');
            const height = svg.attr('height');

            simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(d => d.id).distance(100))
                .force('charge', d3.forceManyBody().strength(-300))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(d => d.size / 2 + 5));

            // Create links
            const link = g.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('line')
                .attr('class', 'link')
                .style('stroke-width', d => Math.sqrt(d.strength * 3));

            // Create nodes
            const node = g.append('g')
                .selectAll('circle')
                .data(nodes)
                .enter().append('circle')
                .attr('class', 'node')
                .attr('r', d => d.size / 2)
                .style('fill', d => typeColors[d.type])
                .style('stroke', '#fff')
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended))
                .on('mouseover', showTooltip)
                .on('mouseout', hideTooltip);

            // Create labels
            const label = g.append('g')
                .selectAll('text')
                .data(nodes)
                .enter().append('text')
                .attr('class', 'node-label')
                .text(d => d.name.length > 15 ? d.name.substring(0, 15) + '...' : d.name);

            simulation.on('tick', () => {
                link
                    .attr('x1', d => d.source.x)
                    .attr('y1', d => d.source.y)
                    .attr('x2', d => d.target.x)
                    .attr('y2', d => d.target.y);

                node
                    .attr('cx', d => d.x)
                    .attr('cy', d => d.y);

                label
                    .attr('x', d => d.x)
                    .attr('y', d => d.y + d.size / 2 + 15);
            });
        }

        function renderRadialLayout(nodes, links) {
            const width = svg.attr('width');
            const height = svg.attr('height');
            const centerX = width / 2;
            const centerY = height / 2;

            // Group nodes by type
            const typeGroups = d3.group(nodes, d => d.type);
            const types = Array.from(typeGroups.keys());
            const angleStep = (2 * Math.PI) / types.length;

            let allPositionedNodes = [];

            types.forEach((type, typeIndex) => {
                const typeNodes = typeGroups.get(type);
                const typeAngle = typeIndex * angleStep;
                const radius = 200;
                const typeX = centerX + Math.cos(typeAngle) * radius;
                const typeY = centerY + Math.sin(typeAngle) * radius;

                // Position nodes in a circle around the type center
                typeNodes.forEach((node, nodeIndex) => {
                    const nodeAngle = (nodeIndex / typeNodes.length) * 2 * Math.PI;
                    const nodeRadius = 80;
                    node.x = typeX + Math.cos(nodeAngle) * nodeRadius;
                    node.y = typeY + Math.sin(nodeAngle) * nodeRadius;
                    node.fx = node.x; // Fix position
                    node.fy = node.y;
                });

                allPositionedNodes = allPositionedNodes.concat(typeNodes);
            });

            // Render with fixed positions
            renderStaticLayout(allPositionedNodes, links);
        }

        function renderHierarchicalLayout(nodes, links) {
            // Simple hierarchical layout - system nodes at top, user nodes below
            const systemNodes = nodes.filter(n => n.isSystem);
            const userNodes = nodes.filter(n => !n.isSystem);
            
            const width = svg.attr('width');
            const height = svg.attr('height');

            // Position system nodes at top
            systemNodes.forEach((node, i) => {
                node.x = (width / (systemNodes.length + 1)) * (i + 1);
                node.y = height * 0.2;
                node.fx = node.x;
                node.fy = node.y;
            });

            // Position user nodes below, grouped by type
            const userTypeGroups = d3.group(userNodes, d => d.type);
            const userTypes = Array.from(userTypeGroups.keys());
            
            userTypes.forEach((type, typeIndex) => {
                const typeNodes = userTypeGroups.get(type);
                const startX = (width / (userTypes.length + 1)) * (typeIndex + 1);
                
                typeNodes.forEach((node, nodeIndex) => {
                    node.x = startX + (nodeIndex - typeNodes.length / 2) * 60;
                    node.y = height * 0.7;
                    node.fx = node.x;
                    node.fy = node.y;
                });
            });

            renderStaticLayout(nodes, links);
        }

        function renderStaticLayout(nodes, links) {
            // Create links
            const link = g.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('line')
                .attr('class', 'link')
                .attr('x1', d => d.source.x || nodes.find(n => n.id === d.source).x)
                .attr('y1', d => d.source.y || nodes.find(n => n.id === d.source).y)
                .attr('x2', d => d.target.x || nodes.find(n => n.id === d.target).x)
                .attr('y2', d => d.target.y || nodes.find(n => n.id === d.target).y)
                .style('stroke-width', d => Math.sqrt(d.strength * 3));

            // Create nodes
            const node = g.append('g')
                .selectAll('circle')
                .data(nodes)
                .enter().append('circle')
                .attr('class', 'node')
                .attr('cx', d => d.x)
                .attr('cy', d => d.y)
                .attr('r', d => d.size / 2)
                .style('fill', d => typeColors[d.type])
                .style('stroke', '#fff')
                .on('mouseover', showTooltip)
                .on('mouseout', hideTooltip);

            // Create labels
            const label = g.append('g')
                .selectAll('text')
                .data(nodes)
                .enter().append('text')
                .attr('class', 'node-label')
                .attr('x', d => d.x)
                .attr('y', d => d.y + d.size / 2 + 15)
                .text(d => d.name.length > 15 ? d.name.substring(0, 15) + '...' : d.name);
        }

        function showTooltip(event, d) {
            const tooltip = document.getElementById('tooltip');
            
            let fieldsHtml = '';
            if (d.fields.length > 0) {
                fieldsHtml = '<div class="fields">Fields: ' + 
                    d.fields.map(f => `<span class="field">${f.name}</span>`).join('') + 
                    '</div>';
            }

            tooltip.innerHTML = `
                <h4>${d.name}</h4>
                <div><strong>Type:</strong> ${d.type.charAt(0).toUpperCase() + d.type.slice(1)}</div>
                ${d.description ? `<div><strong>Description:</strong> ${d.description}</div>` : ''}
                <div><strong>Fields:</strong> ${d.fields.length}</div>
                ${fieldsHtml}
            `;
            
            tooltip.style.left = (event.pageX + 10) + 'px';
            tooltip.style.top = (event.pageY - 10) + 'px';
            tooltip.classList.add('visible');
        }

        function hideTooltip() {
            document.getElementById('tooltip').classList.remove('visible');
        }

        // Drag functions for force layout
        function dragstarted(event, d) {
            if (!event.active && simulation) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active && simulation) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        function setupEventListeners() {
            document.getElementById('view-select').addEventListener('change', (e) => {
                currentView = e.target.value;
                renderDiagram();
            });

            document.getElementById('layout-select').addEventListener('change', (e) => {
                currentLayout = e.target.value;
                renderDiagram();
            });

            document.getElementById('reset-btn').addEventListener('click', () => {
                svg.transition().call(zoom.transform, d3.zoomIdentity);
                renderDiagram();
            });
        }

        // Initialize everything
        initDiagram();
        loadTanaData();

        // Handle window resize
        window.addEventListener('resize', () => {
            const container = document.querySelector('.diagram-container');
            const width = container.clientWidth;
            const height = container.clientHeight;
            
            svg.attr('width', width).attr('height', height);
            renderDiagram();
        });
    </script>
</body>
</html>
