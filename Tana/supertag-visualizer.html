<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tana Supertag Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2d3748;
        }

        .header {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            color: white;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
        }

        .controls {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 1.5rem;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255,255,255,0.8);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-box:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        .view-toggle {
            display: flex;
            background: rgba(255,255,255,0.8);
            border-radius: 12px;
            overflow: hidden;
        }

        .view-btn {
            padding: 0.75rem 1.5rem;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }

        .view-btn.active {
            background: #667eea;
            color: white;
        }

        .supertag-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 2rem;
        }

        .supertag-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .supertag-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .supertag-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f1f5f9;
        }

        .supertag-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-right: 1rem;
            font-weight: bold;
            color: white;
        }

        .supertag-info h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 0.25rem;
        }

        .supertag-type {
            font-size: 0.9rem;
            color: #718096;
            font-weight: 500;
        }

        .supertag-description {
            color: #4a5568;
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-style: italic;
        }

        .fields-section {
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #667eea;
            border-radius: 2px;
            margin-right: 0.5rem;
        }

        .field-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .field-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .field-tag.system {
            background: linear-gradient(135deg, #48bb78, #38a169);
            box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
        }

        .relationships {
            margin-top: 1rem;
        }

        .relationship-item {
            background: #f7fafc;
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            border-left: 3px solid #667eea;
        }

        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: rgba(255,255,255,0.8);
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }

        .loading {
            text-align: center;
            padding: 4rem 2rem;
            color: rgba(255,255,255,0.8);
            font-size: 1.2rem;
        }

        /* Color schemes for different supertag types */
        .person { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .meeting { background: linear-gradient(135deg, #fa709a, #fee140); }
        .task { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .content { background: linear-gradient(135deg, #ffecd2, #fcb69f); }
        .organization { background: linear-gradient(135deg, #667eea, #764ba2); }
        .time { background: linear-gradient(135deg, #48bb78, #38a169); }
        .system { background: linear-gradient(135deg, #718096, #4a5568); }

        @media (max-width: 768px) {
            .supertag-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Supertag Visualizer</h1>
        <p>Explore your Tana supertags, their fields, and relationships</p>
    </div>

    <div class="container">
        <div class="controls">
            <input type="text" class="search-box" placeholder="Search supertags..." id="search-input">
            <div class="view-toggle">
                <button class="view-btn active" data-view="user">My Supertags</button>
                <button class="view-btn" data-view="all">All Supertags</button>
                <button class="view-btn" data-view="system">System Only</button>
            </div>
        </div>

        <div class="supertag-grid" id="supertag-grid">
            <div class="loading">Loading your supertags...</div>
        </div>
    </div>

    <script>
        let tanaData = null;
        let allSupertags = [];
        let currentView = 'user';
        let currentSearch = '';

        // Supertag type classification
        const supertagTypes = {
            person: ['person', 'people', 'contact', 'attendee', 'known', 'referenced'],
            meeting: ['meeting', 'event', 'appointment', 'call'],
            task: ['task', 'todo', 'recurring', 'rtt'],
            content: ['article', 'memo', 'brainstorm', 'prep', 'reflection', 'idea', 'question', 'discuss'],
            organization: ['entity', 'project', 'area', 'relationship', 'h2', 'h3', 'h4', 'h5'],
            time: ['day', 'week', 'daily', 'weekly'],
            system: ['meta', 'field', 'definition', 'data', 'value', 'enum']
        };

        // Load Tana data
        async function loadTanaData() {
            try {
                const response = await fetch('Mehs.json');
                tanaData = await response.json();
                processSupertags();
                renderSupertags();
                setupEventListeners();
            } catch (error) {
                console.error('Error loading Tana data:', error);
                showFileInput();
            }
        }

        function showFileInput() {
            document.getElementById('supertag-grid').innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <div style="background: rgba(255,255,255,0.95); border-radius: 20px; padding: 3rem; backdrop-filter: blur(10px);">
                        <h3 style="color: #4a5568; margin-bottom: 1rem;">Load Your Tana Configuration</h3>
                        <p style="color: #718096; margin-bottom: 2rem;">
                            Please select your Mehs.json file to visualize your supertags:
                        </p>
                        <input type="file" id="file-input" accept=".json" style="
                            padding: 1rem 2rem;
                            border: 2px dashed #cbd5e0;
                            border-radius: 12px;
                            background: #f7fafc;
                            cursor: pointer;
                            font-size: 1rem;
                        ">
                    </div>
                </div>
            `;
            
            document.getElementById('file-input').addEventListener('change', handleFileSelect);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    tanaData = JSON.parse(e.target.result);
                    processSupertags();
                    renderSupertags();
                    setupEventListeners();
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    alert('Error parsing JSON file. Please make sure it\'s a valid Tana export.');
                }
            };
            reader.readAsText(file);
        }

        function processSupertags() {
            if (!tanaData) return;
            
            const docs = tanaData.docs || [];
            const supertags = docs.filter(doc => doc.props._docType === 'tagDef');
            
            allSupertags = supertags.map(supertag => {
                const name = supertag.props.name || supertag.id;
                const isSystem = supertag.id.startsWith('SYS_');
                const type = classifySupertag(name.toLowerCase(), isSystem);
                
                // Get fields for this supertag
                const fields = getFieldsForSupertag(supertag, docs);
                
                // Get relationships
                const relationships = getRelationships(supertag, docs);
                
                return {
                    id: supertag.id,
                    name: name,
                    description: supertag.props.description || '',
                    type: type,
                    isSystem: isSystem,
                    fields: fields,
                    relationships: relationships,
                    childrenCount: supertag.children ? supertag.children.length : 0
                };
            });
        }

        function classifySupertag(name, isSystem) {
            if (isSystem) return 'system';
            
            for (const [type, keywords] of Object.entries(supertagTypes)) {
                if (keywords.some(keyword => name.includes(keyword))) {
                    return type;
                }
            }
            return 'organization'; // default
        }

        function getFieldsForSupertag(supertag, docs) {
            if (!supertag.children) return [];
            
            return supertag.children
                .map(childId => docs.find(doc => doc.id === childId))
                .filter(child => child && (child.props._docType === 'attrDef' || child.props.name))
                .map(field => ({
                    name: field.props.name || field.id,
                    description: field.props.description || '',
                    isSystem: field.id.startsWith('SYS_')
                }))
                .slice(0, 10); // Limit for display
        }

        function getRelationships(supertag, docs) {
            // This is a simplified relationship detection
            const relationships = [];
            
            if (supertag.children) {
                const childCount = supertag.children.length;
                if (childCount > 0) {
                    relationships.push(`Has ${childCount} field${childCount > 1 ? 's' : ''}`);
                }
            }
            
            return relationships;
        }

        function renderSupertags() {
            let filteredSupertags = allSupertags;
            
            // Apply view filter
            if (currentView === 'user') {
                filteredSupertags = filteredSupertags.filter(st => !st.isSystem);
            } else if (currentView === 'system') {
                filteredSupertags = filteredSupertags.filter(st => st.isSystem);
            }
            
            // Apply search filter
            if (currentSearch) {
                filteredSupertags = filteredSupertags.filter(st => 
                    st.name.toLowerCase().includes(currentSearch) ||
                    st.description.toLowerCase().includes(currentSearch)
                );
            }
            
            if (filteredSupertags.length === 0) {
                document.getElementById('supertag-grid').innerHTML = `
                    <div class="empty-state" style="grid-column: 1 / -1;">
                        <h3>No supertags found</h3>
                        <p>Try adjusting your search or view settings</p>
                    </div>
                `;
                return;
            }
            
            const html = filteredSupertags.map(supertag => `
                <div class="supertag-card">
                    <div class="supertag-header">
                        <div class="supertag-icon ${supertag.type}">
                            ${getIconForType(supertag.type)}
                        </div>
                        <div class="supertag-info">
                            <h3>${supertag.name}</h3>
                            <div class="supertag-type">${formatType(supertag.type)} ${supertag.isSystem ? '• System' : '• User'}</div>
                        </div>
                    </div>
                    
                    ${supertag.description ? `<div class="supertag-description">${supertag.description}</div>` : ''}
                    
                    ${supertag.fields.length > 0 ? `
                        <div class="fields-section">
                            <div class="section-title">Fields</div>
                            <div class="field-list">
                                ${supertag.fields.map(field => `
                                    <span class="field-tag ${field.isSystem ? 'system' : ''}" title="${field.description}">
                                        ${field.name}
                                    </span>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                    
                    ${supertag.relationships.length > 0 ? `
                        <div class="relationships">
                            <div class="section-title">Info</div>
                            ${supertag.relationships.map(rel => `
                                <div class="relationship-item">${rel}</div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `).join('');
            
            document.getElementById('supertag-grid').innerHTML = html;
        }

        function getIconForType(type) {
            const icons = {
                person: '👤',
                meeting: '📅',
                task: '✅',
                content: '📝',
                organization: '🏢',
                time: '⏰',
                system: '⚙️'
            };
            return icons[type] || '📋';
        }

        function formatType(type) {
            return type.charAt(0).toUpperCase() + type.slice(1);
        }

        function setupEventListeners() {
            // Search input
            document.getElementById('search-input').addEventListener('input', (e) => {
                currentSearch = e.target.value.toLowerCase();
                renderSupertags();
            });
            
            // View toggle buttons
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    currentView = e.target.dataset.view;
                    renderSupertags();
                });
            });
        }

        // Initialize
        loadTanaData();
    </script>
</body>
</html>
