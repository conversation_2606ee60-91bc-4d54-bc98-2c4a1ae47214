#!/usr/bin/env python3
"""
Simple HTTP server to serve the Tana Explorer locally.
This solves CORS issues when loading local JSON files.
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def main():
    # Set the port
    PORT = 8000
    
    # Get the directory where this script is located
    script_dir = Path(__file__).parent.absolute()
    
    # Change to the script directory
    os.chdir(script_dir)
    
    # Check if required files exist
    html_file = script_dir / "tana-explorer.html"
    json_file = script_dir / "Mehs.json"
    
    if not html_file.exists():
        print("❌ Error: tana-explorer.html not found in current directory")
        sys.exit(1)
    
    if not json_file.exists():
        print("⚠️  Warning: Mehs.json not found in current directory")
        print("   You'll need to select the file manually in the browser")
    
    # Create the server
    Handler = http.server.SimpleHTTPRequestHandler
    
    # Add CORS headers to allow local file access
    class CORSRequestHandler(Handler):
        def end_headers(self):
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            super().end_headers()
    
    try:
        with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
            print(f"🚀 Starting Tana Explorer server...")
            print(f"📁 Serving files from: {script_dir}")
            print(f"🌐 Server running at: http://localhost:{PORT}")
            print(f"📄 Tana Explorer: http://localhost:{PORT}/tana-explorer.html")
            print()
            print("🔧 Press Ctrl+C to stop the server")
            print()
            
            # Open the browser automatically
            url = f"http://localhost:{PORT}/tana-explorer.html"
            print(f"🌍 Opening {url} in your default browser...")
            webbrowser.open(url)
            
            # Start serving
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Error: Port {PORT} is already in use")
            print(f"   Try closing other applications or use a different port")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
