{
  "id": "SYS_T01",
  "name": "supertag",
  "description": "The Core supertag.  The supertag for the supertag nodes",
  "children": null
}
{
  "id": "SYS_T41",
  "name": "tagr app",
  "description": "The tagr app tag, used to mark nodes as shareable libraries",
  "children": [
    "SYS_C01",
    "SYS_C02",
    "SYS_C03"
  ]
}
{
  "id": "SYS_T16",
  "name": "meta information",
  "description": "Information about a node",
  "children": [
    "SYS_T17",
    "SYS_T22",
    "SYS_T19",
    "SYS_T160",
    "SYS_T161",
    "SYS_T20",
    "SYS_T58",
    "SYS_T91",
    "SYS_T114",
    "SYS_T126",
    "SYS_T95",
    "SYS_T111",
    "SYS_T113",
    "SYS_T115",
    "SYS_T92",
    "SYS_T21",
    "SYS_T128",
    "SYS_T83",
    "SYS_T33",
    "SYS_T23",
    "SYS_T25",
    "SYS_T89",
    "SYS_T47",
    "SYS_T34",
    "SYS_T55",
    "SYS_T56",
    "SYS_T37",
    "SYS_T54",
    "SYS_T129",
    "SYS_T48",
    "SYS_T49",
    "SYS_T50",
    "SYS_T84",
    "SYS_T123",
    "SYS_T120",
    "SYS_T156",
    "SYS_T158",
    "SYS_TEV001",
    "SYS_TEV002",
    "SYS_TEV003",
    "SYS_TEV004",
    "SYS_TEV005",
    "SYS_TEV006",
    "SYS_TEV007",
    "SYS_T96",
    "SYS_T108",
    "SYS_T97",
    "SYS_T112",
    "SYS_T93",
    "SYS_T86"
  ]
}
{
  "id": "SYS_T29",
  "name": "row defaults",
  "description": "Node holding column defaults",
  "children": null
}
{
  "id": "SYS_T02",
  "name": "field-definition",
  "description": "Field definition.  All field definitions are instances of this",
  "children": [
    "SYS_A208_T",
    "SYS_A01",
    "SYS_A10",
    "SYS_A02",
    "SYS_T44",
    "SYS_T60",
    "SYS_T82",
    "SYS_T127",
    "SYS_T85",
    "SYS_T90",
    "SYS_A03",
    "SYS_T51",
    "SYS_T52",
    "SYS_T53",
    "SYS_A06",
    "SYS_A08",
    "SYS_T46",
    "SYS_T28",
    "SYS_T30",
    "SYS_T40",
    "SYS_TEV001",
    "SYS_TEV002",
    "SYS_TEV003",
    "SYS_TEV004",
    "SYS_TEV005",
    "SYS_TEV006",
    "SYS_TEV007"
  ]
}
{
  "id": "SYS_T98",
  "name": "meeting (base type)",
  "description": "Meetings, appointments, etc.",
  "children": null
}
{
  "id": "SYS_T99",
  "name": "person (base type)",
  "description": "Persons, attendees, candidates, etc.",
  "children": null
}
{
  "id": "SYS_T100",
  "name": "task (base type)",
  "description": "Todo, tasks, work item etc.",
  "children": null
}
{
  "id": "SYS_T101",
  "name": "organization (base type)",
  "description": "Company, organization, etc.",
  "children": null
}
{
  "id": "SYS_T102",
  "name": "location (base type)",
  "description": "Place, location, etc.",
  "children": null
}
{
  "id": "SYS_T103",
  "name": "event (base type)",
  "description": "Event, happening, etc.",
  "children": null
}
{
  "id": "SYS_T104",
  "name": "project (base type)",
  "description": "Project, etc.",
  "children": null
}
{
  "id": "SYS_T105",
  "name": "topic (base type)",
  "description": "Topic, etc.",
  "children": null
}
{
  "id": "SYS_T117",
  "name": "article (base type)",
  "description": "Article, etc.",
  "children": null
}
{
  "id": "SYS_T118",
  "name": "memo (base type)",
  "description": "Memo, etc.",
  "children": null
}
{
  "id": "SYS_T119",
  "name": "reflection (base type)",
  "description": "Reflection, etc.",
  "children": null
}
{
  "id": "SYS_T124",
  "name": "day (base type)",
  "description": "Day node",
  "children": null
}
{
  "id": "SYS_T125",
  "name": "week (base type)",
  "description": "Week node",
  "children": null
}
{
  "id": "PrE-Wl3Wxw1-",
  "name": "Tags",
  "description": null,
  "children": null
}
{
  "id": "ChTfO3yTpiqj",
  "name": "Person",
  "description": null,
  "children": [
    "tb1LOq_wmkOl",
    "QNURtFbFmg5H"
  ]
}
{
  "id": "gz08mN1LaplR",
  "name": "Meeting",
  "description": null,
  "children": [
    "VjFVm0dNbuYz",
    "wcaS9JSKyuj9",
    "wZG0pwbG1yee",
    "sHIOhhDkMGhQ",
    "HCJOSFcEZ-w_",
    "K2ZEPzzI5pfu",
    "0z_lYKGJ4YbZ",
    "rlXAw_bDe7dJ",
    "mkkkwPm15_3l",
    "KwnWT_8XjJYD",
    "QBSbNRbviAOY"
  ]
}
{
  "id": "4BSykQUtTAuv",
  "name": "Day",
  "description": null,
  "children": null
}
{
  "id": "1h_3iQy37uOR",
  "name": "Tags",
  "description": null,
  "children": null
}
{
  "id": "vAE2Jti8hZKT",
  "name": "Nursing Assesmnet",
  "description": null,
  "children": null
}
{
  "id": "s5EfIr5-luXk",
  "name": "Task",
  "description": null,
  "children": [
    "P9qEbhfRr55c",
    "iYDvZxhvibDZ"
  ]
}
{
  "id": "Ke1xVc8n5u5T",
  "name": "Article",
  "description": null,
  "children": null
}
{
  "id": "vaWShGCFhoR3",
  "name": "Memo",
  "description": null,
  "children": null
}
{
  "id": "rSvIRD9NWcFk",
  "name": "Brainstorm",
  "description": "Capture ideas and questions on the go <i>with voice</i>",
  "children": [
    "8_73CYldhP7B",
    "D2ndB4Z7_xsS"
  ]
}
{
  "id": "7t6e0FNGVO-0",
  "name": "Daily prep",
  "description": "Kickstart your day <i>with voice</i>",
  "children": [
    "u4LvtsIfkIob",
    "nouxkgtfAZS_",
    "KnZnOS01FsqL",
    "lTcn3apQmVM8"
  ]
}
{
  "id": "_3hcSzZdlNJJ",
  "name": "Weekly reflection",
  "description": "Capture your weekly insights and progress <i>with voice</i>",
  "children": [
    "OBnLlisPUg1d",
    "JjU9xtwHaN_z",
    "WDHYpJonRc-a",
    "vZjOCt1fMoq0",
    "0CZg4yv6Rk8h"
  ]
}
{
  "id": "y14ZgGc5Vlg0",
  "name": "Week",
  "description": null,
  "children": null
}
{
  "id": "ITYGHY7GamAA",
  "name": "Person - Known",
  "description": null,
  "children": [
    "FJCWRwBNQVCS",
    "wFnfVJ0WsCT0"
  ]
}
{
  "id": "gPNTbKL8B9RZ",
  "name": "Person - Referenced",
  "description": null,
  "children": null
}
{
  "id": "tDhH4EdVc1Z5",
  "name": "H2 - Area",
  "description": null,
  "children": null
}
{
  "id": "VxbJMU-5ONt5",
  "name": "Relationship",
  "description": "How a person relates to me.",
  "children": null
}
{
  "id": "glCdGZF9t8gY",
  "name": "task (RTT)",
  "description": null,
  "children": [
    "gb8Kd2_kF7yC",
    "3phF8K9LIxOL",
    "Zb0B1qfepVxT",
    "DQeAlVaMg60v",
    "-rzcjYiSQfPn"
  ]
}
{
  "id": "rT4lRQDTDYpU",
  "name": "recurring task (RTT)",
  "description": null,
  "children": [
    "Gtpkmz3fkfm_",
    "Effqx2e0P-pa",
    "5Q-mg_q4lL5M",
    "BgzjJUfEijeL"
  ]
}
{
  "id": "DANeIaR62zgx",
  "name": "Entity",
  "description": null,
  "children": [
    "2_z9qpc9mn2r",
    "uD0epbTNuLzQ",
    "A2R1-WT4ZdtS"
  ]
}
{
  "id": "hLpfGDxTbmxR",
  "name": "To discuss",
  "description": null,
  "children": [
    "awtozE8D3hUI",
    "25Ecop__wV56"
  ]
}
{
  "id": "beLzvxDsyGlF",
  "name": "Idea",
  "description": null,
  "children": null
}
{
  "id": "12YAzHL8cZmf",
  "name": "Question",
  "description": null,
  "children": null
}
{
  "id": "xthbJKoq-v7C",
  "name": "30s",
  "description": null,
  "children": null
}
{
  "id": "7b9wryGT79ek",
  "name": "Integrated Testing",
  "description": null,
  "children": null
}
{
  "id": "BpgRlKzFu8Hc",
  "name": "H2S - Area",
  "description": null,
  "children": [
    "W88LX5M9dFDl",
    "HSvlrVFlH395",
    "4A9yP1wUmyHo"
  ]
}
{
  "id": "_TkLi7fQWUQt",
  "name": "Project",
  "description": null,
  "children": null
}
{
  "id": "GSwPdstM0Soz",
  "name": "H5 Purpose &amp; Principles",
  "description": null,
  "children": null
}
{
  "id": "13L3I65jOlzL",
  "name": "H5S Purpose &amp; Principles",
  "description": null,
  "children": [
    "IFkC_BqswWma"
  ]
}
{
  "id": "Dzt5ZpqjV7Hx",
  "name": "H4 - Vision",
  "description": null,
  "children": null
}
{
  "id": "NZCwWlf5HWan",
  "name": "H3 - Goals &amp; Objectives",
  "description": null,
  "children": null
}
