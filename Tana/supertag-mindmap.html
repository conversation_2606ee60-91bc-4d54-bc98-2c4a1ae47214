<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tana Supertag Mind Map</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        select, button {
            padding: 0.5rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            color: #4a5568;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        select:hover, button:hover {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .mindmap-container {
            width: 100vw;
            height: calc(100vh - 80px);
            position: relative;
            overflow: hidden;
            cursor: grab;
        }

        .mindmap-container.dragging {
            cursor: grabbing;
        }

        .mindmap-canvas {
            position: absolute;
            width: 200%;
            height: 200%;
            transform-origin: center;
            transition: transform 0.3s ease;
        }

        .supertag-card {
            position: absolute;
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 2px solid #e2e8f0;
            min-width: 220px;
            max-width: 280px;
            cursor: pointer;
            transition: all 0.3s;
            z-index: 10;
        }

        .supertag-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .supertag-card.selected {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .card-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin-right: 0.75rem;
            color: white;
            font-weight: bold;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            line-height: 1.3;
        }

        .card-description {
            color: #718096;
            font-size: 0.85rem;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .card-fields {
            margin-top: 1rem;
        }

        .fields-title {
            font-size: 0.8rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field-list {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
        }

        .field-item {
            background: #f8fafc;
            padding: 0.4rem 0.6rem;
            border-radius: 6px;
            font-size: 0.8rem;
            color: #4a5568;
            border-left: 3px solid #e2e8f0;
            cursor: pointer;
            transition: all 0.2s;
        }

        .field-item:hover {
            background: #edf2f7;
            border-left-color: #667eea;
            color: #2d3748;
        }

        .field-item.highlighted {
            background: #667eea;
            color: white;
            border-left-color: #4c51bf;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
        }

        .connection-line {
            position: absolute;
            background: #cbd5e0;
            height: 2px;
            transform-origin: left center;
            z-index: 1;
            opacity: 0.6;
            transition: all 0.3s;
        }

        .connection-line.field-connection {
            background: #667eea;
            opacity: 0.4;
        }

        .connection-line.hierarchy-connection {
            background: #48bb78;
            height: 3px;
            opacity: 0.8;
        }

        .connection-line.highlighted {
            height: 4px;
            opacity: 1;
            box-shadow: 0 0 8px rgba(102, 126, 234, 0.5);
        }

        .central-hub {
            position: absolute;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 8px 40px rgba(0,0,0,0.2);
            z-index: 20;
            min-width: 300px;
        }

        .central-hub h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .central-hub p {
            opacity: 0.9;
            font-size: 1rem;
        }

        .zoom-controls {
            position: absolute;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 100;
        }

        .zoom-btn {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: white;
            color: #4a5568;
            font-size: 1.4rem;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .zoom-btn:hover {
            background: #667eea;
            color: white;
            transform: scale(1.1);
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #4a5568;
            font-size: 1.2rem;
            z-index: 100;
        }

        /* Color schemes for different supertag types */
        .person { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .meeting { background: linear-gradient(135deg, #fa709a, #fee140); }
        .task { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .content { background: linear-gradient(135deg, #ffecd2, #fcb69f); }
        .organization { background: linear-gradient(135deg, #667eea, #764ba2); }
        .time { background: linear-gradient(135deg, #48bb78, #38a169); }
        .system { background: linear-gradient(135deg, #718096, #4a5568); }

        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 0.75rem;
            border-radius: 8px;
            font-size: 0.85rem;
            pointer-events: none;
            z-index: 1000;
            max-width: 250px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .tooltip.visible {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }
            
            .controls {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .supertag-card {
                min-width: 150px;
                max-width: 180px;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Tana Supertag Mind Map</h1>
        <div class="controls">
            <select id="view-select">
                <option value="user">My Supertags</option>
                <option value="all">All Supertags</option>
                <option value="system">System Only</option>
            </select>
            <select id="layout-select">
                <option value="radial">Radial Layout</option>
                <option value="grid">Grid Layout</option>
                <option value="hierarchical">Hierarchical</option>
            </select>
            <button id="reset-btn">Reset View</button>
        </div>
    </div>

    <div class="mindmap-container" id="mindmap-container">
        <div class="mindmap-canvas" id="mindmap-canvas">
            <div class="loading" id="loading">Loading your supertag mind map...</div>
        </div>
        
        <div class="zoom-controls">
            <button class="zoom-btn" id="zoom-in">+</button>
            <button class="zoom-btn" id="zoom-out">−</button>
            <button class="zoom-btn" id="zoom-reset">⌂</button>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        let tanaData = null;
        let allSupertags = [];
        let currentView = 'user';
        let currentLayout = 'radial';
        let currentZoom = 1;
        let currentPan = { x: 0, y: 0 };
        let isDragging = false;
        let dragStart = { x: 0, y: 0 };
        let selectedField = null;

        // Supertag type classification
        const supertagTypes = {
            person: ['person', 'people', 'contact', 'attendee', 'known', 'referenced'],
            meeting: ['meeting', 'event', 'appointment', 'call'],
            task: ['task', 'todo', 'recurring', 'rtt'],
            content: ['article', 'memo', 'brainstorm', 'prep', 'reflection', 'idea', 'question', 'discuss'],
            organization: ['entity', 'project', 'area', 'relationship', 'h2', 'h3', 'h4', 'h5'],
            time: ['day', 'week', 'daily', 'weekly'],
            system: ['meta', 'field', 'definition', 'data', 'value', 'enum']
        };

        // Load Tana data
        async function loadTanaData() {
            try {
                const response = await fetch('Mehs.json');
                tanaData = await response.json();
                processSupertags();
                renderMindMap();
                setupEventListeners();
                document.getElementById('loading').style.display = 'none';
            } catch (error) {
                console.error('Error loading Tana data:', error);
                showFileInput();
            }
        }

        function showFileInput() {
            document.getElementById('loading').innerHTML = `
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 1rem;">Load Your Tana Configuration</h3>
                    <p style="margin-bottom: 1rem; color: #718096;">
                        Please select your Mehs.json file:
                    </p>
                    <input type="file" id="file-input" accept=".json" style="
                        padding: 0.75rem;
                        border: 2px dashed #cbd5e0;
                        border-radius: 8px;
                        background: #f7fafc;
                        cursor: pointer;
                    ">
                </div>
            `;
            
            document.getElementById('file-input').addEventListener('change', handleFileSelect);
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    tanaData = JSON.parse(e.target.result);
                    processSupertags();
                    renderMindMap();
                    setupEventListeners();
                    document.getElementById('loading').style.display = 'none';
                } catch (error) {
                    console.error('Error parsing JSON:', error);
                    alert('Error parsing JSON file.');
                }
            };
            reader.readAsText(file);
        }

        function processSupertags() {
            if (!tanaData) return;

            const docs = tanaData.docs || [];
            const supertags = docs.filter(doc => doc.props._docType === 'tagDef');

            allSupertags = supertags.map(supertag => {
                const name = supertag.props.name || supertag.id;
                const isSystem = supertag.id.startsWith('SYS_');
                const type = classifySupertag(name.toLowerCase(), isSystem);

                // Get fields for this supertag
                const fields = getFieldsForSupertag(supertag, docs);

                // Find parent/child relationships
                const relationships = findSupertagRelationships(supertag, docs, supertags);

                return {
                    id: supertag.id,
                    name: name,
                    description: supertag.props.description || '',
                    type: type,
                    isSystem: isSystem,
                    fields: fields,
                    children: supertag.children || [],
                    relationships: relationships
                };
            });
        }

        function classifySupertag(name, isSystem) {
            if (isSystem) return 'system';
            
            for (const [type, keywords] of Object.entries(supertagTypes)) {
                if (keywords.some(keyword => name.includes(keyword))) {
                    return type;
                }
            }
            return 'organization';
        }

        function getFieldsForSupertag(supertag, docs) {
            if (!supertag.children) return [];

            return supertag.children
                .map(childId => docs.find(doc => doc.id === childId))
                .filter(child => child && (child.props._docType === 'attrDef' || child.props.name))
                .map(field => ({
                    name: field.props.name || field.id,
                    description: field.props.description || '',
                    id: field.id
                }))
                .slice(0, 10); // Increased limit for better display
        }

        function findSupertagRelationships(supertag, docs, allSupertags) {
            const relationships = {
                extends: [], // Supertags this one extends from
                extendedBy: [], // Supertags that extend from this one
                relatedTo: [] // Supertags with shared fields
            };

            // Check if this supertag extends from base types
            const name = supertag.props.name || supertag.id;
            const baseTypes = ['person', 'meeting', 'task', 'organization', 'location', 'event', 'project', 'topic', 'article', 'memo', 'reflection', 'day', 'week'];

            baseTypes.forEach(baseType => {
                if (name.toLowerCase().includes(baseType)) {
                    const baseSupertag = allSupertags.find(st =>
                        (st.props.name || st.id).toLowerCase().includes(`${baseType} (base type)`) ||
                        (st.props.name || st.id).toLowerCase() === baseType
                    );
                    if (baseSupertag && baseSupertag.id !== supertag.id) {
                        relationships.extends.push(baseSupertag.id);
                    }
                }
            });

            return relationships;
        }

        function renderMindMap() {
            const canvas = document.getElementById('mindmap-canvas');
            canvas.innerHTML = '';

            // Filter supertags based on current view
            let filteredSupertags = allSupertags;
            if (currentView === 'user') {
                filteredSupertags = filteredSupertags.filter(st => !st.isSystem);
            } else if (currentView === 'system') {
                filteredSupertags = filteredSupertags.filter(st => st.isSystem);
            }

            // Limit to reasonable number for visualization
            filteredSupertags = filteredSupertags.slice(0, 20);

            if (filteredSupertags.length === 0) {
                canvas.innerHTML = '<div class="loading">No supertags found for current view</div>';
                return;
            }

            // Create central hub
            const centralHub = document.createElement('div');
            centralHub.className = 'central-hub';
            centralHub.innerHTML = `
                <h2>Supertags</h2>
                <p>${filteredSupertags.length} items</p>
            `;
            
            // Position central hub
            const containerWidth = canvas.offsetWidth;
            const containerHeight = canvas.offsetHeight;
            centralHub.style.left = (containerWidth / 2 - 150) + 'px';
            centralHub.style.top = (containerHeight / 2 - 60) + 'px';
            canvas.appendChild(centralHub);

            // Position supertag cards based on layout
            if (currentLayout === 'radial') {
                positionRadial(filteredSupertags, canvas, containerWidth, containerHeight);
            } else if (currentLayout === 'grid') {
                positionGrid(filteredSupertags, canvas, containerWidth, containerHeight);
            } else if (currentLayout === 'hierarchical') {
                positionHierarchical(filteredSupertags, canvas, containerWidth, containerHeight);
            }

            // Create connection lines for shared fields
            createFieldConnections(filteredSupertags, canvas);
        }

        function positionRadial(supertags, canvas, width, height) {
            const centerX = width / 2;
            const centerY = height / 2;
            const radius = Math.min(width, height) * 0.35;
            const angleStep = (2 * Math.PI) / supertags.length;

            supertags.forEach((supertag, index) => {
                const angle = index * angleStep;
                const x = centerX + Math.cos(angle) * radius - 125;
                const y = centerY + Math.sin(angle) * radius - 100;

                const card = createSupertagCard(supertag);
                card.style.left = x + 'px';
                card.style.top = y + 'px';
                card.dataset.x = x;
                card.dataset.y = y;
                canvas.appendChild(card);
            });
        }

        function positionGrid(supertags, canvas, width, height) {
            const cols = Math.ceil(Math.sqrt(supertags.length));
            const rows = Math.ceil(supertags.length / cols);
            const cardWidth = 250;
            const cardHeight = 200;
            const spacingX = width / (cols + 1);
            const spacingY = height / (rows + 1);

            supertags.forEach((supertag, index) => {
                const col = index % cols;
                const row = Math.floor(index / cols);
                const x = spacingX * (col + 1) - cardWidth / 2;
                const y = spacingY * (row + 1) - cardHeight / 2;

                const card = createSupertagCard(supertag);
                card.style.left = x + 'px';
                card.style.top = y + 'px';
                card.dataset.x = x;
                card.dataset.y = y;
                canvas.appendChild(card);
            });
        }

        function positionHierarchical(supertags, canvas, width, height) {
            // Group by type
            const typeGroups = {};
            supertags.forEach(st => {
                if (!typeGroups[st.type]) typeGroups[st.type] = [];
                typeGroups[st.type].push(st);
            });

            const types = Object.keys(typeGroups);
            const levelHeight = height / (types.length + 1);

            types.forEach((type, typeIndex) => {
                const typeSupertags = typeGroups[type];
                const itemWidth = width / (typeSupertags.length + 1);

                typeSupertags.forEach((supertag, itemIndex) => {
                    const x = itemWidth * (itemIndex + 1) - 125;
                    const y = levelHeight * (typeIndex + 1) - 100;

                    const card = createSupertagCard(supertag);
                    card.style.left = x + 'px';
                    card.style.top = y + 'px';
                    card.dataset.x = x;
                    card.dataset.y = y;
                    canvas.appendChild(card);
                });
            });
        }

        function createSupertagCard(supertag) {
            const card = document.createElement('div');
            card.className = 'supertag-card';
            card.dataset.id = supertag.id;

            const icon = getIconForType(supertag.type);

            const fieldsHtml = supertag.fields.length > 0 ? `
                <div class="card-fields">
                    <div class="fields-title">Fields (${supertag.fields.length})</div>
                    <div class="field-list">
                        ${supertag.fields.map(field => `
                            <div class="field-item" data-field="${field.name}" title="${field.description || field.name}">
                                ${field.name}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : '<div class="card-fields"><div class="fields-title">No fields</div></div>';

            card.innerHTML = `
                <div class="card-header">
                    <div class="card-icon ${supertag.type}">${icon}</div>
                    <div class="card-title">${supertag.name}</div>
                </div>
                ${supertag.description ? `<div class="card-description">${supertag.description.substring(0, 60)}${supertag.description.length > 60 ? '...' : ''}</div>` : ''}
                ${fieldsHtml}
            `;

            // Add field hover events
            card.querySelectorAll('.field-item').forEach(fieldItem => {
                fieldItem.addEventListener('mouseenter', () => highlightSharedField(fieldItem.dataset.field));
                fieldItem.addEventListener('mouseleave', () => clearHighlights());
            });

            return card;
        }

        function getIconForType(type) {
            const icons = {
                person: '👤',
                meeting: '📅',
                task: '✅',
                content: '📝',
                organization: '🏢',
                time: '⏰',
                system: '⚙️'
            };
            return icons[type] || '📋';
        }

        function createFieldConnections(supertags, canvas) {
            // Create hierarchy connections first
            supertags.forEach(supertag => {
                if (supertag.relationships.extends.length > 0) {
                    supertag.relationships.extends.forEach(parentId => {
                        const parentSupertag = supertags.find(st => st.id === parentId);
                        if (parentSupertag) {
                            createConnectionLine(parentId, supertag.id, 'hierarchy', canvas, 'hierarchy-connection');
                        }
                    });
                }
            });

            // Find shared fields
            const fieldMap = new Map();

            supertags.forEach(supertag => {
                supertag.fields.forEach(field => {
                    if (!fieldMap.has(field.name)) {
                        fieldMap.set(field.name, []);
                    }
                    fieldMap.get(field.name).push(supertag.id);
                });
            });

            // Create connections for fields shared by multiple supertags
            fieldMap.forEach((supertagIds, fieldName) => {
                if (supertagIds.length > 1) {
                    // Create connections between all supertags that share this field
                    for (let i = 0; i < supertagIds.length; i++) {
                        for (let j = i + 1; j < supertagIds.length; j++) {
                            createConnectionLine(supertagIds[i], supertagIds[j], fieldName, canvas, 'field-connection');
                        }
                    }
                }
            });
        }

        function createConnectionLine(fromId, toId, fieldName, canvas, connectionType = 'field-connection') {
            const fromCard = canvas.querySelector(`[data-id="${fromId}"]`);
            const toCard = canvas.querySelector(`[data-id="${toId}"]`);

            if (!fromCard || !toCard) return;

            const fromX = parseInt(fromCard.dataset.x) + 140;
            const fromY = parseInt(fromCard.dataset.y) + 100;
            const toX = parseInt(toCard.dataset.x) + 140;
            const toY = parseInt(toCard.dataset.y) + 100;

            const line = document.createElement('div');
            line.className = `connection-line ${connectionType}`;
            line.dataset.field = fieldName;
            line.dataset.from = fromId;
            line.dataset.to = toId;
            line.dataset.type = connectionType;

            const length = Math.sqrt(Math.pow(toX - fromX, 2) + Math.pow(toY - fromY, 2));
            const angle = Math.atan2(toY - fromY, toX - fromX) * 180 / Math.PI;

            line.style.left = fromX + 'px';
            line.style.top = fromY + 'px';
            line.style.width = length + 'px';
            line.style.transform = `rotate(${angle}deg)`;

            // Add tooltip for hierarchy connections
            if (connectionType === 'hierarchy-connection') {
                line.title = 'Inheritance relationship';
            } else {
                line.title = `Shared field: ${fieldName}`;
            }

            canvas.appendChild(line);
        }

        function highlightSharedField(fieldName) {
            selectedField = fieldName;

            // Highlight all field items with this name
            document.querySelectorAll('.field-item').forEach(item => {
                if (item.dataset.field === fieldName) {
                    item.classList.add('highlighted');
                } else {
                    item.classList.remove('highlighted');
                }
            });

            // Highlight connection lines for this field
            document.querySelectorAll('.connection-line').forEach(line => {
                if (line.dataset.field === fieldName && line.dataset.type === 'field-connection') {
                    line.classList.add('highlighted');
                } else {
                    line.classList.remove('highlighted');
                }
            });
        }

        function clearHighlights() {
            selectedField = null;
            document.querySelectorAll('.field-item').forEach(item => {
                item.classList.remove('highlighted');
            });
            document.querySelectorAll('.connection-line').forEach(line => {
                line.classList.remove('highlighted');
            });
        }

        function setupEventListeners() {
            // View and layout controls
            document.getElementById('view-select').addEventListener('change', (e) => {
                currentView = e.target.value;
                renderMindMap();
            });

            document.getElementById('layout-select').addEventListener('change', (e) => {
                currentLayout = e.target.value;
                renderMindMap();
            });

            document.getElementById('reset-btn').addEventListener('click', () => {
                currentZoom = 1;
                currentPan = { x: 0, y: 0 };
                updateCanvasTransform();
                renderMindMap();
            });

            // Zoom controls
            document.getElementById('zoom-in').addEventListener('click', () => {
                currentZoom = Math.min(currentZoom * 1.2, 3);
                updateCanvasTransform();
            });

            document.getElementById('zoom-out').addEventListener('click', () => {
                currentZoom = Math.max(currentZoom / 1.2, 0.3);
                updateCanvasTransform();
            });

            document.getElementById('zoom-reset').addEventListener('click', () => {
                currentZoom = 1;
                currentPan = { x: 0, y: 0 };
                updateCanvasTransform();
            });

            // Pan functionality
            const container = document.getElementById('mindmap-container');
            container.addEventListener('mousedown', startDrag);
            container.addEventListener('mousemove', drag);
            container.addEventListener('mouseup', endDrag);
            container.addEventListener('mouseleave', endDrag);

            // Zoom with mouse wheel
            container.addEventListener('wheel', (e) => {
                e.preventDefault();
                const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                currentZoom = Math.max(0.3, Math.min(3, currentZoom * zoomFactor));
                updateCanvasTransform();
            });
        }

        function startDrag(e) {
            if (e.target.closest('.supertag-card') || e.target.closest('.zoom-btn')) return;
            isDragging = true;
            dragStart = { x: e.clientX - currentPan.x, y: e.clientY - currentPan.y };
            document.getElementById('mindmap-container').classList.add('dragging');
        }

        function drag(e) {
            if (!isDragging) return;
            currentPan.x = e.clientX - dragStart.x;
            currentPan.y = e.clientY - dragStart.y;
            updateCanvasTransform();
        }

        function endDrag() {
            isDragging = false;
            document.getElementById('mindmap-container').classList.remove('dragging');
        }

        function updateCanvasTransform() {
            const canvas = document.getElementById('mindmap-canvas');
            canvas.style.transform = `translate(${currentPan.x}px, ${currentPan.y}px) scale(${currentZoom})`;
        }

        // Initialize
        loadTanaData();
    </script>
</body>
</html>
