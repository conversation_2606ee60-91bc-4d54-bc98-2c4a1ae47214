{
  "id": "glum7ujN7ZIw",
  "name": "Event status",
  "description": null,
  "children": [
    "6GcApD2UqiT_",
    "GQSe8PtZajUU",
    "sFsQ9HPSHYLT"
  ]
}
{
  "id": "Ye1tNngcQzSZ",
  "name": "Description",
  "description": null,
  "children": [
    "7xG3yGEHSKjo",
    "-PXqdkxzU_by"
  ]
}
{
  "id": "6R-9L6F6sA6z",
  "name": "No",
  "description": null,
  "children": null
}
{
  "id": "W93wAnGl-yCc",
  "name": "Text",
  "description": null,
  "children": null
}
{
  "id": "LuA4vFEE5YK0",
  "name": "Show sidebar",
  "description": "Show expanded sidebar",
  "children": [
    "bK8hgwrElzqP"
  ]
}
{
  "id": "66AC5wR52db7",
  "name": "General Tags",
  "description": null,
  "children": [
    "DmxaATxd-Is7",
    "36P_geZSeNjQ",
    "QUUj_6flsd68",
    "L-UTYAKR5-xy",
    "Rd3iNVTuxJEg"
  ]
}
{
  "id": "T_KDoAHAJw1Y",
  "name": "Show completed items",
  "description": "Showing completed items in lists",
  "children": [
    "J_TORg8d8c4l"
  ]
}
{
  "id": "-fk8kPm58cfw",
  "name": "Enable dark mode",
  "description": "Come to the dark side",
  "children": [
    "F4oJ-jDG6Evj"
  ]
}
{
  "id": "2Fv1HdApYlYC",
  "name": "Use system theme",
  "description": "Use system light/dark mode setting",
  "children": [
    "pNsOtq8ZXOAi"
  ]
}
{
  "id": "OFCI1gBVXpgy",
  "name": "Quick start guides",
  "description": "Get up to speed fast",
  "children": [
    "Nf1hrRHbV9Jz"
  ]
}
{
  "id": "6zpU5knvh2fM",
  "name": "Audio-enabled fields",
  "description": "Learn how to enable audio for fields",
  "children": [
    "BoWv0H4_SNPC"
  ]
}
{
  "id": "AtlC36qQMx--",
  "name": "Custom commands",
  "description": "Learn how to create command nodes",
  "children": [
    "bXk4iGSs7-O-"
  ]
}
{
  "id": "yIS0axGmIZyo",
  "name": "Attendees field",
  "description": "The field to use to store the list of attendees",
  "children": [
    "ElXB5Ugotwj5"
  ]
}
{
  "id": "BrCVkydmeV9F",
  "name": "Attendee supertag",
  "description": "Imported attendees will try and match an email in this tag",
  "children": [
    "W0chEe6Spfsj"
  ]
}
{
  "id": "E-f20mKHNy1J",
  "name": "Attendee email field",
  "description": "The field in the attendee tag that has the emails to match against",
  "children": [
    "5xRorMgrK0wm"
  ]
}
{
  "id": "6b8RcbAWL5rS",
  "name": "Date field",
  "description": "The field to use to store the date of the event",
  "children": [
    "KuiMWzWwnjll"
  ]
}
{
  "id": "dFx2xdNctB7B",
  "name": "Event meet link URL",
  "description": "The attribute that stores the meet link",
  "children": [
    "5i0L7ZkIB1eK"
  ]
}
{
  "id": "1xFyltzjsVOL",
  "name": "Event details field",
  "description": "The attribute that stores the event details/description",
  "children": [
    "IkGmaiKhvKAJ"
  ]
}
{
  "id": "tiCO6-UUmvh3",
  "name": "Event Status field",
  "description": "The attribute that stores the event status",
  "children": [
    "d-_4vpAlrUHH"
  ]
}
{
  "id": "UaHXJdDDJJ26",
  "name": "Event Classification Config",
  "description": "The config for event classification",
  "children": null
}
{
  "id": "HQKH6Slyxk-w",
  "name": "Event Destination",
  "description": "Where you want new events to live. Write day if you want them to go to your day node.",
  "children": null
}
{
  "id": "Q__PwkOwS9Zc",
  "name": "Contacts Destination",
  "description": "Where you want Google contacts to live.",
  "children": null
}
{
  "id": "6DiqUvJFoneA",
  "name": "Enabled Calendars",
  "description": "The Google calendar IDs that are enabled for import",
  "children": null
}
{
  "id": "sJuJQB1HWINZ",
  "name": "Warn when editing referenced nodes",
  "description": "Enables the reference counter indicator when editing references",
  "children": [
    "x5_YXfe_cdmd"
  ]
}
{
  "id": "ZpXEcjfUi3Fz",
  "name": "Show release notes",
  "description": "Shows release notes whenever something significantly new is released",
  "children": [
    "lD4frZK78NCw"
  ]
}
{
  "id": "LMcRhCrA9jtB",
  "name": "Show URL embeds",
  "description": "Shows URL embeds by default if on",
  "children": [
    "O6LX35KYIu_R"
  ]
}
{
  "id": "S56BZM_qq1Hd",
  "name": "Show reference counter",
  "description": "Shows reference counter next to nodes that have references",
  "children": [
    "Nvo1XPSB2jvp"
  ]
}
{
  "id": "P6uoEtuaeC6C",
  "name": "Context",
  "description": null,
  "children": [
    "VDBlSZxJ3S2M",
    "tOlAfE3_rCGm",
    "xZVzfztL4Phj",
    "2szaEI-VzEl5"
  ]
}
{
  "id": "XTqphSB-6_WP",
  "name": "Relationship",
  "description": "How does this person related to me?",
  "children": [
    "n07aQ3gG1yPm",
    "mkZOINQEa1rr",
    "74EqdNc3Su1x",
    "Y4AEfCngwU4p"
  ]
}
{
  "id": "NnT8KVcKHuiz",
  "name": "Event supertag",
  "description": "Imported events will be tagged with this supertag",
  "children": [
    "oOPCcL11jngP"
  ]
}
{
  "id": "RdtrVgOIF3MD",
  "name": "Default journal",
  "description": null,
  "children": null
}
{
  "id": "VdQcCWXGLemA",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "Tj7BNcWJmu_3",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "IwTMWRA0_26e",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "sBcNzTRlsDyN",
  "name": "Alias",
  "description": null,
  "children": null
}
{
  "id": "4sCMjnj8w4od",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "auWRZ4PY0bEb",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "0e4WoRNKavhp",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "09J_nBQScnBV",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "ErrdSHkD_FuG",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "IMAdMfKKL2JZ",
  "name": "#task\nAND (PARENT: @Tag",
  "description": null,
  "children": null
}
{
  "id": "osMdEYhHpUL2",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "lib0RZebLRwC",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "r4dUtnKO5P-R",
  "name": "Area",
  "description": null,
  "children": [
    "k85sRYa1bJPi",
    "cCKE_6SFn3yL",
    "72RwUQfzH3c9",
    "y1ui2VcpyBXR"
  ]
}
{
  "id": "IvzotFKv4eCP",
  "name": "Entity",
  "description": null,
  "children": [
    "xOEGgCcgcJA9",
    "Hxd_h56yPUsx",
    "1n28QpwCgAh1",
    "xh-NtzQiJZR_"
  ]
}
{
  "id": "v9XSeFTYgSCU",
  "name": "Project",
  "description": null,
  "children": [
    "xYmGs8_RxIKa",
    "w8vZPBrC5tin",
    "-QRB5dX4FoSt",
    "Me2lssC44GZD"
  ]
}
{
  "id": "TmQVYrV1Qywl",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "Tt0GqN5RGiJo",
  "name": "area",
  "description": null,
  "children": null
}
{
  "id": "rCfDyfDuCteY",
  "name": null,
  "description": null,
  "children": null
}
{
  "id": "VcICh7slmwvh",
  "name": "Area",
  "description": null,
  "children": null
}
{
  "id": "YwRSGQXzNRWw",
  "name": "Entity",
  "description": null,
  "children": null
}
{
  "id": "RJn4zO9DOJtz",
  "name": "Scheduled for",
  "description": null,
  "children": [
    "RKpgQTcivceB"
  ]
}
{
  "id": "bL3T1MJLb5tv",
  "name": "Status",
  "description": null,
  "children": [
    "t3t6gFXZbaEN",
    "sDD3-w_oPeD6"
  ]
}
{
  "id": "XfBOphc7vIDn",
  "name": "Assigned to",
  "description": null,
  "children": [
    "cAi7Ybp9QI5h"
  ]
}
{
  "id": "UAi4BQkrp3ig",
  "name": "Priority",
  "description": null,
  "children": [
    "XcUk2zmbeVWd",
    "gBJGHRGH8_7A"
  ]
}
{
  "id": "FwL2YBa5vuEu",
  "name": "Estimated hours",
  "description": null,
  "children": [
    "7UAmgMuSt8NU"
  ]
}
{
  "id": "0JAnAgmtyLqP",
  "name": "Actual hours",
  "description": null,
  "children": [
    "4TqZmEdsqNWd"
  ]
}
{
  "id": "akMd_KPrJWBM",
  "name": "Project",
  "description": null,
  "children": null
}
{
  "id": "Ij5MLhNMdAEU",
  "name": "H2 Area",
  "description": null,
  "children": [
    "N-tuG_8WurIh",
    "aDy61h4Gqvtq",
    "kMesC4jLNW1I",
    "4JXrSHLCl1so"
  ]
}
{
  "id": "e1muariEg1jY",
  "name": "Project",
  "description": null,
  "children": null
}
{
  "id": "BQ6xIptlgyfL",
  "name": "Entit",
  "description": null,
  "children": null
}
{
  "id": "S09M5eWqBKNI",
  "name": "H5 Purpose &amp; Principles",
  "description": null,
  "children": [
    "AxC0O91N2bbc",
    "1g4CwjXnuWsW",
    "ShYNDoljZ6bn",
    "qd049_hs76a-"
  ]
}
