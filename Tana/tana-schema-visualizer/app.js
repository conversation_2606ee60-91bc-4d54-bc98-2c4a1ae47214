/**
 * Transforms the Tana Intermediate Format (TIF) data into a
 * Cytoscape.js-compatible format.
 * @param {object} tifData The TIF data loaded from schema.tif.json.
 * @returns {Array} An array of elements for Cytoscape.
 */
function transformTifToCytoscape(tifData) {
  const elements = [];
  const createdFieldIds = new Set();

  if (!tifData || !tifData.supertags) {
    return elements;
  }

  // First Pass: Create all nodes (supertags and unique fields)
  tifData.supertags.forEach(supertag => {
    // Create a node for the supertag
    elements.push({
      data: {
        id: supertag.id,
        label: supertag.name,
        type: 'supertag',
        color: supertag.color,
        description: supertag.description || ''
      },
      classes: 'supertag'
    });

    // Create nodes for unique fields
    if (supertag.fields) {
      supertag.fields.forEach(field => {
        if (!createdFieldIds.has(field.id)) {
          elements.push({
            data: {
              id: field.id,
              label: field.name,
              type: 'field',
              fieldType: field.fieldType,
              description: field.description || '',
              config: field.config
            },
            classes: 'field ' + field.fieldType
          });
          createdFieldIds.add(field.id);
        }
      });
    }
  });

  // Second Pass: Create all edges (relationships)
  tifData.supertags.forEach(supertag => {
    // Create edges from supertag to its fields
    if (supertag.fields) {
      supertag.fields.forEach(field => {
        elements.push({
          data: {
            source: supertag.id,
            target: field.id
          },
          classes: 'field-edge'
        });
      });
    }

    // Create inheritance edges (extends)
    if (supertag.extends && supertag.extends.length > 0) {
      supertag.extends.forEach(parentId => {
        elements.push({
          data: {
            source: supertag.id,
            target: parentId
          },
          classes: 'extends-edge'
        });
      });
    }
  });

  // Third Pass: Create instance edges (for fields linking to other supertags)
  tifData.supertags.forEach(supertag => {
    if (supertag.fields) {
      supertag.fields.forEach(field => {
        if (field.config && field.config.sourceSupertagId) {
          elements.push({
            data: {
              source: field.id,
              target: field.config.sourceSupertagId
            },
            classes: 'instance-edge'
          });
        }
      });
    }
  });

  return elements;
}


// Main function to load data and initialize the visualizer
async function main() {
  try {
    const response = await fetch('./schema.tif.json');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const tifData = await response.json();

    const elements = transformTifToCytoscape(tifData);

    // Phase 3: Rendering the Core Graph Visualization
    const style = [
      {
        selector: 'node',
        style: {
          'label': 'data(label)',
          'color': '#f0f0f0',
          'font-size': '12px',
          'text-valign': 'center',
          'text-halign': 'center',
          'text-outline-color': '#2d2d2d',
          'text-outline-width': 2
        }
      },
      {
        selector: '.supertag',
        style: {
          'background-color': 'data(color)',
          'shape': 'round-rectangle',
          'width': 'label',
          'height': '30px',
          'padding': '10px',
          'border-width': '1px',
          'border-color': '#777'
        }
      },
      {
        selector: '.field',
        style: {
          'background-color': '#555',
          'shape': 'round-rectangle',
          'width': 'label',
          'height': '25px',
          'padding': '8px',
          'font-size': '10px',
          'border-width': '1px',
          'border-color': '#888'
        }
      },
      {
        selector: 'edge',
        style: {
          'width': 1.5,
          'line-color': '#777',
          'curve-style': 'bezier',
          'target-arrow-color': '#777'
        }
      },
      {
        selector: '.field-edge',
        style: {
          'width': 1,
          'line-color': '#666'
        }
      },
      {
        selector: '.extends-edge',
        style: {
          'line-color': '#87CEEB',
          'line-style': 'solid',
          'target-arrow-shape': 'triangle',
          'target-arrow-color': '#87CEEB',
          'width': 2
        }
      },
      {
        selector: '.instance-edge',
        style: {
          'line-color': '#90EE90',
          'line-style': 'dotted',
          'target-arrow-shape': 'tee',
          'target-arrow-color': '#90EE90',
          'width': 2
        }
      },
      {
        selector: 'node:hover',
        style: {
          'border-width': '3px',
          'border-color': '#FCE700'
        }
      }
    ];

    const layout = {
        name: 'cose',
        idealEdgeLength: 100,
        nodeOverlap: 20,
        refresh: 20,
        fit: true,
        padding: 30,
        randomize: false,
        componentSpacing: 100,
        nodeRepulsion: 400000,
        edgeElasticity: 100,
        nestingFactor: 5,
        gravity: 80
      };

    const cy = cytoscape({
        container: document.getElementById('cy'),
        elements: elements,
        style: style,
        layout: layout
    });

    // Phase 4: Implementing User Interactivity
    const detailsPanel = document.getElementById('details-panel');

    // Hover effects are now handled by the stylesheet's 'node:hover' selector.
    cy.on('mouseover', 'node', (evt) => {
        document.getElementById('cy').style.cursor = 'pointer';
    });
    cy.on('mouseout', 'node', (evt) => {
        document.getElementById('cy').style.cursor = 'default';
    });

    // Tap handler for nodes
    cy.on('tap', 'node', (evt) => {
        const nodeData = evt.target.data();

        let html = `<h3>${nodeData.label}</h3>`;
        if (nodeData.description) {
            html += `<p><em>${nodeData.description}</em></p>`;
        }
        html += `<p><small>ID: ${nodeData.id}</small></p><hr>`;

        if (nodeData.type === 'supertag') {
            html += '<h4>Fields:</h4>';
            // Get fields connected to this supertag
            const fields = evt.target.successors('edge.field-edge').targets();
            if (fields.length > 0) {
                html += '<ul>';
                fields.forEach(field => {
                    html += `<li>${field.data('label')}</li>`;
                });
                html += '</ul>';
            } else {
                html += '<p>No fields defined.</p>';
            }
        } else if (nodeData.type === 'field') {
            html += `<p><strong>Field Type:</strong> ${nodeData.fieldType}</p>`;
            const config = nodeData.config;
            if (config) {
                if (config.options && config.options.length > 0) {
                    html += '<strong>Options:</strong><ul>';
                    config.options.forEach(opt => {
                        html += `<li>${opt}</li>`;
                    });
                    html += '</ul>';
                }
                if (config.sourceSupertagId) {
                    const sourceSupertag = cy.getElementById(config.sourceSupertagId);
                    const sourceName = sourceSupertag.length > 0 ? sourceSupertag.data('label') : config.sourceSupertagId;
                    html += `<p><strong>Instance of:</strong> ${sourceName}</p>`;
                }
            }
        }

        detailsPanel.innerHTML = html;
        detailsPanel.style.display = 'block';
    });

    // Tap handler for background to hide panel
    cy.on('tap', (evt) => {
        if (evt.target === cy) {
            detailsPanel.style.display = 'none';
        }
    });

  } catch (error) {
    console.error('Error loading or processing Tana schema:', error);
    const container = document.getElementById('cy');
    if(container) {
        container.innerHTML = `<div style="padding: 20px; text-align: center; color: red;">
            <h2>Error</h2>
            <p>Could not load or process Tana schema file (schema.tif.json).</p>
            <p><em>${error.message}</em></p>
            <p>Please ensure the file exists in the 'tana-schema-visualizer' directory and is a valid JSON file.</p>
            </div>`;
    }
  }
}

// Run the main function
main();
