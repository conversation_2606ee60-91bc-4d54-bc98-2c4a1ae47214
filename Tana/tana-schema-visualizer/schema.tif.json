{"supertags": [{"id": "st_task_123", "name": "#task", "description": "A task to be completed. This is a sample supertag.", "color": "#fb8072", "extends": [], "fields": [{"id": "f_status_456", "name": "Status", "description": "The current state of the task.", "fieldType": "options", "config": {"options": ["To Do", "In Progress", "Done"]}}, {"id": "f_assignee_789", "name": "Assignee", "description": "The person responsible for the task.", "fieldType": "instance", "config": {"sourceSupertagId": "st_person_789"}}, {"id": "f_due_date_999", "name": "Due Date", "description": "When the task should be completed.", "fieldType": "date", "config": {}}]}, {"id": "st_person_789", "name": "#person", "description": "Represents a person in the system.", "color": "#80b1d3", "extends": [], "fields": [{"id": "f_email_111", "name": "Email", "description": "The person's email address.", "fieldType": "email", "config": {}}]}, {"id": "st_project_456", "name": "#project", "description": "A project containing tasks.", "color": "#b3de69", "extends": [], "fields": []}]}